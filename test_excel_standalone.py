#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Excel منفصل
"""

from flask import Flask, make_response
from openpyxl import Workbook
from io import BytesIO

app = Flask(__name__)

@app.route('/')
def home():
    return "خادم اختبار Excel يعمل!"

@app.route('/test-excel')
def test_excel():
    try:
        # إنشاء ملف Excel
        wb = Workbook()
        ws = wb.active
        
        # بيانات بسيطة
        ws['A1'] = "اختبار Excel"
        ws['A2'] = "يعمل بنجاح!"
        ws['A3'] = "تاريخ الاختبار: 2025-01-27"
        
        # إضافة جدول بسيط
        headers = ['ت', 'الاسم', 'الدرجة']
        for col, header in enumerate(headers, 1):
            ws.cell(row=5, column=col, value=header)
        
        # بيانات وهمية
        data = [
            [1, "أحمد محمد", 85],
            [2, "فاطمة علي", 92],
            [3, "محمد أحمد", 78],
            [4, "عائشة سالم", 95],
            [5, "يوسف خالد", 88]
        ]
        
        for row_idx, row_data in enumerate(data, 6):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)
        
        # حفظ في الذاكرة
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        # إرجاع الملف
        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = 'attachment; filename="test_excel.xlsx"'
        
        return response
        
    except Exception as e:
        return f"خطأ: {str(e)}"

if __name__ == '__main__':
    print("🚀 بدء خادم اختبار Excel...")
    print("📊 اختبر Excel على: http://127.0.0.1:5001/test-excel")
    print("🏠 الصفحة الرئيسية: http://127.0.0.1:5001/")
    app.run(debug=True, port=5001)
