from flask import render_template, request, redirect, url_for, jsonify, flash, make_response
from models import db, Student, Class, Grade, Subject, Period, StudentGrade
import random
import string
import re
from datetime import datetime
from export_utils import (
    export_students_to_excel, export_grades_to_excel, export_numbers_to_excel,
    export_student_grades_report, export_final_results_report
)

def register_routes(app):
    """تسجيل جميع مسارات التطبيق"""

    # إضافة متغير now إلى جميع القوالب
    @app.context_processor
    def inject_now():
        return {'now': datetime.now()}

    # الصفحة الرئيسية
    @app.route('/')
    def index():
        return render_template('index.html')

    # ===== إدارة الطلاب =====

    # عرض قائمة الطلاب
    @app.route('/students')
    def list_students():
        class_id = request.args.get('class_id', type=int)
        if class_id:
            students = Student.query.filter_by(class_id=class_id).all()
            selected_class = Class.query.get_or_404(class_id)
        else:
            students = []
            selected_class = None

        classes = Class.query.all()
        return render_template('students/list.html', students=students, classes=classes, selected_class=selected_class)

    # إضافة طالب جديد
    @app.route('/students/add', methods=['GET', 'POST'])
    def add_student():
        if request.method == 'POST':
            name = request.form.get('name')
            national_id = request.form.get('national_id')
            class_id = request.form.get('class_id')

            # التحقق من البيانات
            if not name or not national_id or not class_id:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('add_student'))

            # التحقق من عدم تكرار الرقم الوطني
            existing_student = Student.query.filter_by(national_id=national_id).first()
            if existing_student:
                flash('الرقم الوطني موجود بالفعل', 'error')
                return redirect(url_for('add_student'))

            # إنشاء طالب جديد
            student = Student(
                name=name,
                national_id=national_id,
                class_id=class_id
            )

            db.session.add(student)
            db.session.commit()

            flash('تمت إضافة الطالب بنجاح', 'success')
            return redirect(url_for('list_students', class_id=class_id))

        classes = Class.query.all()
        return render_template('students/add.html', classes=classes)

    # تعديل بيانات طالب
    @app.route('/students/edit/<int:id>', methods=['GET', 'POST'])
    def edit_student(id):
        student = Student.query.get_or_404(id)

        if request.method == 'POST':
            name = request.form.get('name')
            national_id = request.form.get('national_id')
            class_id = request.form.get('class_id')

            # التحقق من البيانات
            if not name or not national_id or not class_id:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('edit_student', id=id))

            # التحقق من عدم تكرار الرقم الوطني
            existing_student = Student.query.filter(Student.national_id == national_id, Student.id != id).first()
            if existing_student:
                flash('الرقم الوطني موجود بالفعل', 'error')
                return redirect(url_for('edit_student', id=id))

            # تحديث بيانات الطالب
            student.name = name
            student.national_id = national_id
            student.class_id = class_id

            db.session.commit()

            flash('تم تحديث بيانات الطالب بنجاح', 'success')
            return redirect(url_for('list_students', class_id=class_id))

        classes = Class.query.all()
        return render_template('students/edit.html', student=student, classes=classes)

    # حذف طالب
    @app.route('/students/delete/<int:id>', methods=['POST'])
    def delete_student(id):
        student = Student.query.get_or_404(id)
        class_id = student.class_id

        db.session.delete(student)
        db.session.commit()

        flash('تم حذف الطالب بنجاح', 'success')
        return redirect(url_for('list_students', class_id=class_id))

    # البحث عن طالب
    @app.route('/students/search')
    def search_students():
        query = request.args.get('query', '')

        if not query:
            return jsonify([])

        # البحث بالاسم أو الرقم الوطني
        students = Student.query.filter(
            (Student.name.like(f'%{query}%')) |
            (Student.national_id.like(f'%{query}%'))
        ).all()

        results = []
        for student in students:
            results.append({
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'class_name': student.class_.name
            })

        return jsonify(results)

    # ===== إدارة الصفوف الدراسية =====

    # عرض قائمة الصفوف الدراسية
    @app.route('/grades')
    def list_grades():
        grades = Grade.query.all()
        return render_template('grades/list.html', grades=grades)

    # عرض درجات طالب واحد
    @app.route('/grades/student/<int:student_id>')
    def student_grades(student_id):
        from utils.calculations import calculate_total_grade, has_second_chance_exam

        student = Student.query.get_or_404(student_id)
        periods = Period.query.all()
        subjects = Subject.query.filter_by(grade_id=student.class_.grade_id).all()

        # حساب المجموع الكلي للطالب
        total_info = calculate_total_grade(student_id, student.class_id)

        # إضافة معلومة الدور الثاني
        if total_info:
            total_info['has_second_chance'] = has_second_chance_exam(student_id)

        return render_template('grades/student.html',
                             student=student,
                             periods=periods,
                             subjects=subjects,
                             total_info=total_info)

    # إضافة صف دراسي جديد
    @app.route('/grades/add', methods=['GET', 'POST'])
    def add_grade():
        if request.method == 'POST':
            name = request.form.get('name')
            level = request.form.get('level', type=int)
            track = request.form.get('track')

            # التحقق من البيانات
            if not name or not level or not track:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('add_grade'))

            # إنشاء صف دراسي جديد
            new_grade = Grade(
                name=name,
                level=level,
                track=track
            )

            db.session.add(new_grade)
            db.session.commit()

            flash('تمت إضافة الصف الدراسي بنجاح', 'success')
            return redirect(url_for('list_grades'))

        return render_template('grades/add.html')

    # تعديل صف دراسي
    @app.route('/grades/edit/<int:id>', methods=['GET', 'POST'])
    def edit_grade(id):
        grade = Grade.query.get_or_404(id)

        if request.method == 'POST':
            name = request.form.get('name')
            level = request.form.get('level', type=int)
            track = request.form.get('track')

            # التحقق من البيانات
            if not name or not level or not track:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('edit_grade', id=id))

            # تحديث بيانات الصف الدراسي
            grade.name = name
            grade.level = level
            grade.track = track

            db.session.commit()

            flash('تم تحديث بيانات الصف الدراسي بنجاح', 'success')
            return redirect(url_for('list_grades'))

        return render_template('grades/edit.html', grade=grade)

    # حذف صف دراسي
    @app.route('/grades/delete/<int:id>', methods=['POST'])
    def delete_grade(id):
        grade = Grade.query.get_or_404(id)

        # التحقق من عدم وجود فصول مرتبطة بالصف الدراسي
        if Class.query.filter_by(grade_id=id).first():
            flash('لا يمكن حذف الصف الدراسي لأنه يحتوي على فصول', 'error')
            return redirect(url_for('list_grades'))

        # التحقق من عدم وجود مواد مرتبطة بالصف الدراسي
        if Subject.query.filter_by(grade_id=id).first():
            flash('لا يمكن حذف الصف الدراسي لأنه يحتوي على مواد دراسية', 'error')
            return redirect(url_for('list_grades'))

        db.session.delete(grade)
        db.session.commit()

        flash('تم حذف الصف الدراسي بنجاح', 'success')
        return redirect(url_for('list_grades'))

    # ===== إدارة الفصول =====

    # عرض قائمة الفصول
    @app.route('/classes')
    def list_classes():
        grade_id = request.args.get('grade_id', type=int)
        if grade_id:
            classes = Class.query.filter_by(grade_id=grade_id).all()
            selected_grade = Grade.query.get_or_404(grade_id)
        else:
            classes = Class.query.all()
            selected_grade = None

        grades = Grade.query.all()
        return render_template('classes/list.html', classes=classes, grades=grades, selected_grade=selected_grade)

    # إضافة فصل جديد
    @app.route('/classes/add', methods=['GET', 'POST'])
    def add_class():
        if request.method == 'POST':
            name = request.form.get('name')
            grade_id = request.form.get('grade_id')
            academic_year = request.form.get('academic_year')

            # التحقق من البيانات
            if not name or not grade_id or not academic_year:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('add_class'))

            # إنشاء فصل جديد
            new_class = Class(
                name=name,
                grade_id=grade_id,
                academic_year=academic_year
            )

            db.session.add(new_class)
            db.session.commit()

            flash('تمت إضافة الفصل بنجاح', 'success')
            return redirect(url_for('list_classes', grade_id=grade_id))

        grades = Grade.query.all()
        return render_template('classes/add.html', grades=grades)

    # تعديل فصل
    @app.route('/classes/edit/<int:id>', methods=['GET', 'POST'])
    def edit_class(id):
        class_obj = Class.query.get_or_404(id)

        if request.method == 'POST':
            name = request.form.get('name')
            grade_id = request.form.get('grade_id')
            academic_year = request.form.get('academic_year')

            # التحقق من البيانات
            if not name or not grade_id or not academic_year:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('edit_class', id=id))

            # تحديث بيانات الفصل
            class_obj.name = name
            class_obj.grade_id = grade_id
            class_obj.academic_year = academic_year

            db.session.commit()

            flash('تم تحديث بيانات الفصل بنجاح', 'success')
            return redirect(url_for('list_classes', grade_id=grade_id))

        grades = Grade.query.all()
        return render_template('classes/edit.html', class_obj=class_obj, grades=grades)

    # حذف فصل
    @app.route('/classes/delete/<int:id>', methods=['POST'])
    def delete_class(id):
        class_obj = Class.query.get_or_404(id)
        grade_id = class_obj.grade_id

        # التحقق من عدم وجود طلاب في الفصل
        if class_obj.students:
            flash('لا يمكن حذف الفصل لأنه يحتوي على طلاب', 'error')
            return redirect(url_for('list_classes', grade_id=grade_id))

        db.session.delete(class_obj)
        db.session.commit()

        flash('تم حذف الفصل بنجاح', 'success')
        return redirect(url_for('list_classes', grade_id=grade_id))

    # ===== إدارة المواد الدراسية =====

    # عرض قائمة المواد
    @app.route('/subjects')
    def list_subjects():
        grade_id = request.args.get('grade_id', type=int)
        if grade_id:
            subjects = Subject.query.filter_by(grade_id=grade_id).all()
            selected_grade = Grade.query.get_or_404(grade_id)
        else:
            subjects = Subject.query.all()
            selected_grade = None

        grades = Grade.query.all()
        return render_template('subjects/list.html', subjects=subjects, grades=grades, selected_grade=selected_grade)

    # إضافة مادة جديدة
    @app.route('/subjects/add', methods=['GET', 'POST'])
    def add_subject():
        if request.method == 'POST':
            name = request.form.get('name')
            grade_id = request.form.get('grade_id')
            total_mark = request.form.get('total_mark', type=int, default=100)

            # التحقق من البيانات
            if not name or not grade_id:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('add_subject'))

            # إنشاء مادة جديدة
            new_subject = Subject(
                name=name,
                grade_id=grade_id,
                total_mark=total_mark
            )

            db.session.add(new_subject)
            db.session.commit()

            flash('تمت إضافة المادة بنجاح', 'success')
            return redirect(url_for('list_subjects', grade_id=grade_id))

        grades = Grade.query.all()
        return render_template('subjects/add.html', grades=grades)

    # تعديل مادة
    @app.route('/subjects/edit/<int:id>', methods=['GET', 'POST'])
    def edit_subject(id):
        subject = Subject.query.get_or_404(id)

        if request.method == 'POST':
            name = request.form.get('name')
            grade_id = request.form.get('grade_id')
            total_mark = request.form.get('total_mark', type=int, default=100)

            # التحقق من البيانات
            if not name or not grade_id:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('edit_subject', id=id))

            # تحديث بيانات المادة
            subject.name = name
            subject.grade_id = grade_id
            subject.total_mark = total_mark

            db.session.commit()

            flash('تم تحديث بيانات المادة بنجاح', 'success')
            return redirect(url_for('list_subjects', grade_id=grade_id))

        grades = Grade.query.all()
        return render_template('subjects/edit.html', subject=subject, grades=grades)

    # حذف مادة
    @app.route('/subjects/delete/<int:id>', methods=['POST'])
    def delete_subject(id):
        subject = Subject.query.get_or_404(id)
        grade_id = subject.grade_id

        # التحقق من عدم وجود درجات مرتبطة بالمادة
        if StudentGrade.query.filter_by(subject_id=id).first():
            flash('لا يمكن حذف المادة لأنها مرتبطة بدرجات طلاب', 'error')
            return redirect(url_for('list_subjects', grade_id=grade_id))

        db.session.delete(subject)
        db.session.commit()

        flash('تم حذف المادة بنجاح', 'success')
        return redirect(url_for('list_subjects', grade_id=grade_id))

    # ===== إدارة الدرجات =====

    # الحصول على معلومات الصف الدراسي للفصل
    @app.route('/api/class/<int:class_id>/grade')
    def get_class_grade_info(class_id):
        class_obj = Class.query.get_or_404(class_id)
        return jsonify({'grade_id': class_obj.grade_id})

    # الحصول على المواد حسب الفصل
    @app.route('/api/class/<int:class_id>/subjects')
    def get_subjects_by_class(class_id):
        # الحصول على الفصل
        class_obj = Class.query.get_or_404(class_id)

        # الحصول على المواد المرتبطة بالصف الدراسي
        subjects = Subject.query.filter_by(grade_id=class_obj.grade_id).all()

        # تحويل المواد إلى قائمة
        subject_list = []
        for subject in subjects:
            subject_list.append({
                'id': subject.id,
                'name': subject.name,
                'grade_id': subject.grade_id,
                'total_mark': subject.total_mark
            })

        return jsonify({'subjects': subject_list})

    # عرض صفحة رصد الدرجات
    @app.route('/grades/record')
    def record_grades():
        classes = Class.query.all()
        periods = Period.query.all()

        return render_template('grades/record_new.html', classes=classes, periods=periods)

    # عرض صفحة درجات فصل معين (هذا المسار كان مفقودًا)
    @app.route('/grades/class/<int:class_id>')
    def view_class_grades(class_id):
        # يمكنك هنا عرض صفحة جديدة تظهر درجات جميع طلاب الفصل
        # أو إعادة التوجيه إلى صفحة رصد الدرجات مع تحديد الفصل مسبقًا
        flash(f"تم طلب عرض درجات الفصل رقم {class_id} (لم يتم تنفيذ هذه الصفحة بعد)", "info")
        return redirect(url_for('record_grades'))

    # عرض صفحة تجميع درجات الفصلين (الدور الأول)


    # عرض صفحة تجميع درجات الفصلين - جميع المواد
    @app.route('/grades/combine-semesters-all')
    def combine_semesters_all():
        # الحصول على جميع الصفوف الدراسية (بدلاً من الفصول)
        grades = Grade.query.all()

        return render_template('grades/combine_semesters_all.html', grades=grades)

    # الحصول على جميع البيانات المجمعة لجميع الطلاب والمواد
    @app.route('/grades/combine-semesters/all-data')
    def get_all_combined_data():
        grade_id = request.args.get('class_id', type=int)  # نبقي اسم المعامل class_id للتوافق مع الواجهة

        if not grade_id:
            return jsonify({'error': 'يرجى تحديد الصف'}), 400

        # الحصول على الصف الدراسي المحدد
        selected_grade = Grade.query.get_or_404(grade_id)

        # الحصول على جميع المواد للصف الدراسي
        subjects = Subject.query.filter_by(grade_id=grade_id).order_by(Subject.id).all()

        # الحصول على جميع الفصول في الصف الدراسي
        all_classes_in_grade = Class.query.filter_by(grade_id=grade_id).all()
        class_ids = [cls.id for cls in all_classes_in_grade]

        # الحصول على جميع الطلاب في جميع فصول الصف الدراسي
        students = Student.query.filter(Student.class_id.in_(class_ids)).order_by(Student.class_id, Student.name).all()
        
        # الحصول على فترات الفصل الأول والثاني
        first_semester = Period.query.filter(
            Period.name.contains('الفصل الأول')
        ).first()
        
        second_semester = Period.query.filter(
            Period.name.contains('الفصل الثاني')
        ).first()
        
        if not first_semester or not second_semester:
            return jsonify({'error': 'لم يتم العثور على فترات الفصل الأول والثاني'}), 400
        
        # بناء البيانات المجمعة
        # إنشاء اسم يعكس جميع فصول الصف الدراسي
        grade_name = selected_grade.name if selected_grade else "الصف الدراسي"
        class_names = [cls.name for cls in all_classes_in_grade]
        combined_class_name = f"{grade_name} - جميع الفصول ({', '.join(class_names)})"

        result_data = {
            'class_name': combined_class_name,
            'subjects': [],
            'students': []
        }
        
        grand_total_possible = 0
        # إضافة معلومات المواد
        for subject in subjects:
            result_data['subjects'].append({
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            })
            grand_total_possible += subject.total_mark
        
        # إضافة بيانات الطلاب
        for student in students:
            from utils.calculations import has_second_chance_exam

            student_data = {
                'id': student.id,
                'name': student.name,
                'secret_number': student.secret_number,
                'seat_number': student.seat_number,
                'class_name': student.class_.name,  # إضافة اسم الفصل
                'subjects_grades': {},
                'has_second_chance': has_second_chance_exam(student.id)
            }
            
            student_grand_total = 0
            failed_subjects_indices = []

            # جلب درجات الطالب لكل مادة
            for idx, subject in enumerate(subjects):
                # درجات الفصل الأول
                first_grade = StudentGrade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject.id,
                    period_id=first_semester.id
                ).first()
                
                # درجات الفصل الثاني
                second_grade = StudentGrade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject.id,
                    period_id=second_semester.id
                ).first()
                
                # حساب الدرجات المجمعة
                first_classwork = first_grade.classwork_mark if first_grade and first_grade.classwork_mark is not None else None
                first_exam = first_grade.exam_mark if first_grade and first_grade.exam_mark is not None else None
                second_classwork = second_grade.classwork_mark if second_grade and second_grade.classwork_mark is not None else None
                second_exam = second_grade.exam_mark if second_grade and second_grade.exam_mark is not None else None
                
                # حساب المجاميع فقط إذا كانت هناك درجات
                combined_classwork = None
                combined_exam = None
                total_mark = None
                is_passed = False
                
                # حساب مجموع أعمال السنة إذا كانت هناك درجات
                if first_classwork is not None or second_classwork is not None:
                    classwork_sum = 0
                    
                    if first_classwork is not None:
                        classwork_sum += first_classwork
                        
                    if second_classwork is not None:
                        classwork_sum += second_classwork
                        
                    combined_classwork = classwork_sum
                
                # حساب مجموع الامتحانات إذا كانت هناك درجات
                if first_exam is not None or second_exam is not None:
                    exam_sum = 0
                    
                    if first_exam is not None:
                        exam_sum += first_exam
                        
                    if second_exam is not None:
                        exam_sum += second_exam
                        
                    combined_exam = exam_sum
                
                # حساب المجموع الكلي
                if combined_classwork is not None and combined_exam is not None:
                    total_mark = combined_classwork + combined_exam
                    is_passed = total_mark >= (subject.total_mark * 0.5)
                elif combined_classwork is not None:
                    total_mark = combined_classwork
                    is_passed = total_mark >= (subject.total_mark * 0.5)
                elif combined_exam is not None:
                    total_mark = combined_exam
                    is_passed = total_mark >= (subject.total_mark * 0.5)
                
                if total_mark is not None:
                    student_grand_total += total_mark
                
                student_data['subjects_grades'][subject.id] = {
                    'first_semester': {
                        'classwork_mark': first_classwork,
                        'exam_mark': first_exam
                    },
                    'second_semester': {
                        'classwork_mark': second_classwork,
                        'exam_mark': second_exam
                    },
                    'combined_classwork': round(combined_classwork, 1) if combined_classwork is not None else None,
                    'combined_exam': round(combined_exam, 1) if combined_exam is not None else None,
                    'total_mark': round(total_mark, 1) if total_mark is not None else None,
                    'is_passed': is_passed
                }

                if not is_passed:
                    failed_subjects_indices.append(idx + 1)

            # حساب المجموع الكلي والنسبة والنتيجة النهائية للطالب
            student_data['student_grand_total'] = round(student_grand_total, 1)
            student_data['student_percentage'] = round((student_grand_total / grand_total_possible) * 100, 2) if grand_total_possible > 0 else 0

            if not failed_subjects_indices:
                student_data['final_result'] = 'ناجح'
            else:
                failed_subjects_str = ', '.join(map(str, failed_subjects_indices))
                student_data['final_result'] = f'له دور ثان ({failed_subjects_str})'

            result_data['students'].append(student_data)
        
        return jsonify(result_data)

    # الحصول على قائمة الطلاب لرصد الدرجات
    @app.route('/grades/students')
    def get_students_for_grading():
        class_id = request.args.get('class_id', type=int)
        subject_id = request.args.get('subject_id', type=int)
        period_id = request.args.get('period_id', type=int)
        semester_type = request.args.get('semester_type', 'combined')  # القيمة الافتراضية هي 'combined'

        if not class_id or not subject_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الفصل والمادة والفترة'}), 400

        # الحصول على الفترة المحددة
        period = Period.query.get_or_404(period_id)

        # التحقق من الفترة السابقة (إذا كانت الفترة الحالية هي الفصل الثاني)
        if "ثاني" in period.name or "ثانية" in period.name:
            # طباعة معلومات تشخيصية
            print(f"الفترة الحالية: {period.name} ({period.academic_year})")

            # البحث عن الفترة السابقة (الفصل الأول) بطريقة أكثر مرونة
            # أولاً، نحاول البحث بالاسم المطابق
            previous_period_name = period.name.replace("ثاني", "أول").replace("ثانية", "أولى")
            previous_period = Period.query.filter_by(name=previous_period_name, academic_year=period.academic_year).first()

            # إذا لم نجد الفترة السابقة، نحاول البحث بطريقة أخرى
            if not previous_period:
                # البحث عن أي فترة تحتوي على كلمة "أول" أو "أولى" في نفس السنة الدراسية
                previous_period = Period.query.filter(
                    (Period.name.like('%أول%') | Period.name.like('%أولى%')) &
                    (Period.academic_year == period.academic_year)
                ).first()

            # طباعة معلومات الفترة السابقة
            if previous_period:
                print(f"الفترة السابقة: {previous_period.name} ({previous_period.academic_year})")
                print(f"حالة اعتماد الفترة السابقة: {'معتمدة' if previous_period.is_approved else 'غير معتمدة'}")

                # التحقق من اعتماد الفترة السابقة
                if not previous_period.is_approved:
                    return jsonify({
                        'error': 'لا يمكن رصد درجات الفصل الثاني قبل اعتماد درجات الفصل الأول',
                        'period_name': previous_period.name
                    }), 403
            else:
                print(f"لم يتم العثور على أي فترة للفصل الأول في السنة الدراسية {period.academic_year}")

        # الحصول على الفصل المحدد
        selected_class = Class.query.get_or_404(class_id)

        # الحصول على المادة
        subject = Subject.query.get_or_404(subject_id)

        # تحديد الطلاب بناءً على نوع الفصل (مجمع أو منفصل)
        if semester_type == 'combined':
            # في حالة الفصل المجمع، نحصل على جميع الطلاب في نفس الصف الدراسي (Grade)
            students = Student.query.join(Class).filter(Class.grade_id == selected_class.grade_id).all()
        else:
            # في حالة الفصول المنفصلة، نحصل على الطلاب في الفصل المحدد فقط
            students = Student.query.filter_by(class_id=class_id).all()

        student_list = []
        for student in students:
            # البحث عن درجة الطالب إن وجدت
            grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=False
            ).first()

            student_data = {
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'secret_number': student.secret_number,
                'seat_number': student.seat_number,
                'class_name': student.class_.name,
                'classwork_mark': grade.classwork_mark if grade else None,
                'exam_mark': grade.exam_mark if grade else None,
                'total_mark': grade.total_mark if grade else None,
                'is_passed': grade.is_passed if grade else None
            }

            student_list.append(student_data)

        return jsonify({
            'students': student_list,
            'subject': {
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            }
        })

    # حفظ درجات الطلاب
    @app.route('/grades/save', methods=['POST'])
    def save_grades():
        data = request.json
        subject_id = data.get('subject_id')
        period_id = data.get('period_id')
        semester_type = data.get('semester_type', 'combined')  # القيمة الافتراضية هي 'combined'
        grade_type = data.get('grade_type', 'both')  # القيمة الافتراضية هي 'both'
        grades = data.get('grades', [])

        # التحقق من وجود المادة
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'success': False, 'message': 'لم يتم العثور على المادة المحددة'})

        # الحصول على الفترة المحددة
        period = Period.query.get_or_404(period_id)

        # التحقق من حالة اعتماد الفترة
        if period.is_approved:
            return jsonify({'success': False, 'message': 'لا يمكن تعديل درجات فترة معتمدة. يرجى فك الاعتماد أولاً.'})

        # التحقق من الفترة السابقة (إذا كانت الفترة الحالية هي الفصل الثاني)
        if "ثاني" in period.name or "ثانية" in period.name:
            # طباعة معلومات تشخيصية
            print(f"الفترة الحالية: {period.name} ({period.academic_year})")

            # البحث عن الفترة السابقة (الفصل الأول) بطريقة أكثر مرونة
            # أولاً، نحاول البحث بالاسم المطابق
            previous_period_name = period.name.replace("ثاني", "أول").replace("ثانية", "أولى")
            previous_period = Period.query.filter_by(name=previous_period_name, academic_year=period.academic_year).first()

            # إذا لم نجد الفترة السابقة، نحاول البحث بطريقة أخرى
            if not previous_period:
                # البحث عن أي فترة تحتوي على كلمة "أول" أو "أولى" في نفس السنة الدراسية
                previous_period = Period.query.filter(
                    (Period.name.like('%أول%') | Period.name.like('%أولى%')) &
                    (Period.academic_year == period.academic_year)
                ).first()

            # طباعة معلومات الفترة السابقة
            if previous_period:
                print(f"الفترة السابقة: {previous_period.name} ({previous_period.academic_year})")
                print(f"حالة اعتماد الفترة السابقة: {'معتمدة' if previous_period.is_approved else 'غير معتمدة'}")

                # التحقق من اعتماد الفترة السابقة
                if not previous_period.is_approved:
                    return jsonify({
                        'success': False,
                        'message': f'لا يمكن رصد درجات الفصل الثاني قبل اعتماد درجات الفصل الأول ({previous_period.name})'
                    })
            else:
                print(f"لم يتم العثور على أي فترة للفصل الأول في السنة الدراسية {period.academic_year}")

        for grade_data in grades:
            student_id = grade_data.get('student_id')

            # تحديد الدرجات بناءً على نوع الدرجات المطلوب رصدها
            classwork_mark = grade_data.get('classwork_mark') if grade_type in ['both', 'classwork'] else None
            exam_mark = grade_data.get('exam_mark') if grade_type in ['both', 'exam'] else None

            # البحث عن درجة الطالب أو إنشاء واحدة جديدة
            grade = StudentGrade.query.filter_by(
                student_id=student_id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=False
            ).first()

            if not grade:
                # إنشاء درجة جديدة مع ربطها بالمادة
                grade = StudentGrade(
                    student_id=student_id,
                    subject_id=subject_id,
                    period_id=period_id,
                    is_second_chance=False
                )
                # ربط المادة بالدرجة
                grade.subject = subject
                db.session.add(grade)

            # تحديث الدرجات (تحويل النصوص إلى أرقام)
            try:
                # تحديث درجة أعمال الفصل فقط إذا كان نوع الدرجات المطلوب رصدها يتضمن أعمال الفصل
                if grade_type in ['both', 'classwork'] and classwork_mark is not None and str(classwork_mark).strip() != '':
                    grade.classwork_mark = float(classwork_mark)

                # تحديث درجة الامتحان فقط إذا كان نوع الدرجات المطلوب رصدها يتضمن الامتحان
                if grade_type in ['both', 'exam'] and exam_mark is not None and str(exam_mark).strip() != '':
                    grade.exam_mark = float(exam_mark)
            except (ValueError, TypeError) as e:
                print(f"خطأ في تحويل الدرجات: {e}")
                print(f"classwork_mark: {classwork_mark}, exam_mark: {exam_mark}")
                return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحويل الدرجات. تأكد من إدخال أرقام صحيحة.'})

            # حساب المجموع وتحديد النجاح أو الرسوب
            grade.calculate_total()

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حفظ الدرجات بنجاح'})

    # ===== تجميع درجات الفصلين (الدور الأول) =====

    # الحصول على درجات الطلاب للفصلين (الدور الأول)
    @app.route('/grades/combine-semesters/students')
    def get_students_for_combining():
        class_id = request.args.get('class_id')
        subject_id = request.args.get('subject_id')

        # التحقق من البيانات
        if not class_id or not subject_id:
            return jsonify({
                'error': 'بيانات غير مكتملة'
            }), 400

        # الحصول على الفصل والمادة
        class_obj = Class.query.get_or_404(class_id)
        subject = Subject.query.get_or_404(subject_id)

        # الحصول على الفترات الدراسية (الفصل الأول والثاني)
        first_semester = Period.query.filter(
            (Period.name.like('%أول%') | Period.name.like('%أولى%')) &
            (Period.name.like('%فصل%')) &
            (Period.academic_year == class_obj.academic_year)
        ).first()

        second_semester = Period.query.filter(
            (Period.name.like('%ثاني%') | Period.name.like('%ثانية%')) &
            (Period.name.like('%فصل%')) &
            (Period.academic_year == class_obj.academic_year)
        ).first()

        # التحقق من وجود الفترات الدراسية
        if not first_semester or not second_semester:
            return jsonify({
                'error': 'لم يتم العثور على الفترات الدراسية المطلوبة'
            }), 404

        # التحقق من اعتماد الفصلين
        if not first_semester.is_approved or not second_semester.is_approved:
            return jsonify({
                'error': 'يجب اعتماد درجات الفصلين قبل تجميعها'
            }), 403

        # الحصول على الطلاب في الفصل
        students = Student.query.filter_by(class_id=class_id).all()

        # تجميع البيانات
        students_data = []
        for student in students:
            # الحصول على درجات الفصل الأول
            first_semester_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=first_semester.id
            ).first()

            # الحصول على درجات الفصل الثاني
            second_semester_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=second_semester.id
            ).first()

            # حساب المجموع
            combined_classwork = None
            combined_exam = None
            total_mark = None
            is_passed = None

            if first_semester_grade and second_semester_grade:
                # حساب مجموع أعمال الفصل
                if first_semester_grade.classwork_mark is not None and second_semester_grade.classwork_mark is not None:
                    combined_classwork = first_semester_grade.classwork_mark + second_semester_grade.classwork_mark

                # حساب مجموع الامتحان
                if first_semester_grade.exam_mark is not None and second_semester_grade.exam_mark is not None:
                    combined_exam = first_semester_grade.exam_mark + second_semester_grade.exam_mark

                # حساب المجموع الكلي
                if combined_classwork is not None and combined_exam is not None:
                    # التحقق من شرط الامتحان (40% من درجة الامتحان)
                    exam_percentage = subject.total_mark * 0.6  # 60% من الدرجة الكلية للمادة
                    min_exam_mark = exam_percentage * 0.4  # 40% من درجة الامتحان

                    if combined_exam >= min_exam_mark:
                        total_mark = combined_classwork + combined_exam

                        # التحقق من شرط النجاح (50% من الدرجة الكلية)
                        min_total_mark = subject.total_mark * 0.5  # 50% من الدرجة الكلية
                        is_passed = total_mark >= min_total_mark

            # إضافة بيانات الطالب
            student_data = {
                'id': student.id,
                'name': student.name,
                'secret_number': student.secret_number,
                'seat_number': student.seat_number,
                'first_semester': {
                    'classwork_mark': first_semester_grade.classwork_mark if first_semester_grade else None,
                    'exam_mark': first_semester_grade.exam_mark if first_semester_grade else None
                } if first_semester_grade else None,
                'second_semester': {
                    'classwork_mark': second_semester_grade.classwork_mark if second_semester_grade else None,
                    'exam_mark': second_semester_grade.exam_mark if second_semester_grade else None
                } if second_semester_grade else None,
                'combined_classwork': combined_classwork,
                'combined_exam': combined_exam,
                'total_mark': total_mark,
                'is_passed': is_passed
            }

            students_data.append(student_data)

        return jsonify({
            'subject': {
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            },
            'students': students_data
        })

    # حفظ نتائج تجميع الفصلين (الدور الأول)
    @app.route('/grades/combine-semesters/save', methods=['POST'])
    def save_combined_semesters():
        data = request.json

        # الحصول على البيانات
        class_id = data.get('class_id')
        subject_id = data.get('subject_id')

        # التحقق من البيانات
        if not class_id or not subject_id:
            return jsonify({
                'success': False,
                'message': 'بيانات غير مكتملة'
            })

        # الحصول على الفصل والمادة
        class_obj = Class.query.get_or_404(class_id)
        subject = Subject.query.get_or_404(subject_id)

        # الحصول على الفترات الدراسية (الفصل الأول والثاني والدور الأول)
        first_semester = Period.query.filter(
            (Period.name.like('%أول%') | Period.name.like('%أولى%')) &
            (Period.name.like('%فصل%')) &
            (Period.academic_year == class_obj.academic_year)
        ).first()

        second_semester = Period.query.filter(
            (Period.name.like('%ثاني%') | Period.name.like('%ثانية%')) &
            (Period.name.like('%فصل%')) &
            (Period.academic_year == class_obj.academic_year)
        ).first()

        first_round = Period.query.filter(
            (Period.name.like('%أول%') | Period.name.like('%أولى%')) &
            (Period.name.like('%دور%')) &
            (Period.academic_year == class_obj.academic_year)
        ).first()

        # التحقق من وجود الفترات الدراسية
        if not first_semester or not second_semester or not first_round:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على الفترات الدراسية المطلوبة'
            })

        # التحقق من اعتماد الفصلين
        if not first_semester.is_approved or not second_semester.is_approved:
            return jsonify({
                'success': False,
                'message': 'يجب اعتماد درجات الفصلين قبل تجميعها'
            })

        # الحصول على الطلاب في الفصل
        students = Student.query.filter_by(class_id=class_id).all()

        # حفظ النتائج
        for student in students:
            # الحصول على درجات الفصل الأول
            first_semester_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=first_semester.id
            ).first()

            # الحصول على درجات الفصل الثاني
            second_semester_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=second_semester.id
            ).first()

            # حساب المجموع
            combined_classwork = None
            combined_exam = None
            total_mark = None
            is_passed = None

            if first_semester_grade and second_semester_grade:
                # حساب مجموع أعمال الفصل
                if first_semester_grade.classwork_mark is not None and second_semester_grade.classwork_mark is not None:
                    combined_classwork = first_semester_grade.classwork_mark + second_semester_grade.classwork_mark

                # حساب مجموع الامتحان
                if first_semester_grade.exam_mark is not None and second_semester_grade.exam_mark is not None:
                    combined_exam = first_semester_grade.exam_mark + second_semester_grade.exam_mark

                # حساب المجموع الكلي
                if combined_classwork is not None and combined_exam is not None:
                    # التحقق من شرط الامتحان (40% من درجة الامتحان)
                    exam_percentage = subject.total_mark * 0.6  # 60% من الدرجة الكلية للمادة
                    min_exam_mark = exam_percentage * 0.4  # 40% من درجة الامتحان

                    if combined_exam >= min_exam_mark:
                        total_mark = combined_classwork + combined_exam

                        # التحقق من شرط النجاح (50% من الدرجة الكلية)
                        min_total_mark = subject.total_mark * 0.5  # 50% من الدرجة الكلية
                        is_passed = total_mark >= min_total_mark

            # حفظ النتائج في الدور الأول
            first_round_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=first_round.id
            ).first()

            if not first_round_grade:
                first_round_grade = StudentGrade(
                    student_id=student.id,
                    subject_id=subject_id,
                    period_id=first_round.id
                )
                db.session.add(first_round_grade)

            first_round_grade.classwork_mark = combined_classwork
            first_round_grade.exam_mark = combined_exam
            first_round_grade.total_mark = total_mark
            first_round_grade.is_passed = is_passed

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ نتائج تجميع الفصلين بنجاح'
        })

    # ===== الدور الثاني =====

    # عرض قائمة الطلاب المتعثرين
    @app.route('/second-chance')
    def second_chance_students():
        # الحصول على جميع الصفوف الدراسية (بدلاً من الفصول)
        grades = Grade.query.all()
        subjects = Subject.query.all()
        periods = Period.query.all()

        return render_template('grades/second_chance.html', grades=grades, subjects=subjects, periods=periods)

    # الحصول على قائمة الطلاب المتعثرين (النظام الجديد)
    @app.route('/second-chance/students-new')
    def get_second_chance_students():
        grade_id = request.args.get('class_id', type=int)  # نبقي اسم المعامل class_id للتوافق مع الواجهة
        subject_id = request.args.get('subject_id', type=int)

        if not grade_id or not subject_id:
            return jsonify({'error': 'يرجى تحديد الصف والمادة'}), 400

        # الحصول على الصف الدراسي والمادة
        selected_grade = Grade.query.get_or_404(grade_id)
        subject = Subject.query.get_or_404(subject_id)

        # الحصول على فترات الفصل الأول والثاني
        first_semester = Period.query.filter(
            Period.name == 'الفصل الأول'
        ).first()

        second_semester = Period.query.filter(
            Period.name == 'الفصل الثاني'
        ).first()

        if not first_semester or not second_semester:
            return jsonify({'error': 'لم يتم العثور على فترات الفصل الأول والثاني'}), 400

        # الحصول على جميع الفصول في الصف الدراسي
        all_classes_in_grade = Class.query.filter_by(grade_id=grade_id).all()
        class_ids = [cls.id for cls in all_classes_in_grade]

        # الحصول على جميع الطلاب في جميع فصول الصف الدراسي
        students = Student.query.filter(Student.class_id.in_(class_ids)).order_by(Student.class_id, Student.name).all()

        student_list = []

        for student in students:
            # جلب درجات الطالب في الفصلين
            first_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=first_semester.id
            ).first()

            second_grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=second_semester.id
            ).first()

            # حساب النتيجة المجمعة للفصلين
            first_classwork = first_grade.classwork_mark if first_grade and first_grade.classwork_mark is not None else 0
            first_exam = first_grade.exam_mark if first_grade and first_grade.exam_mark is not None else 0
            second_classwork = second_grade.classwork_mark if second_grade and second_grade.classwork_mark is not None else 0
            second_exam = second_grade.exam_mark if second_grade and second_grade.exam_mark is not None else 0

            # حساب المجموع المجمع
            combined_classwork = first_classwork + second_classwork
            combined_exam = first_exam + second_exam
            total_combined = combined_classwork + combined_exam

            # تحديد إذا كان الطالب راسب (يحتاج دور ثان)
            # التحقق من شرط الامتحان (40% من درجة الامتحان)
            exam_percentage = subject.total_mark * 0.6  # 60% من الدرجة الكلية للمادة
            min_exam_mark = exam_percentage * 0.4  # 40% من درجة الامتحان
            min_total_mark = subject.total_mark * 0.5  # 50% من الدرجة الكلية

            # الطالب راسب إذا لم يحقق شرط الامتحان أو شرط المجموع الكلي
            is_failed = (combined_exam < min_exam_mark) or (total_combined < min_total_mark)

            # إضافة الطلاب الراسبين فقط
            if is_failed:
                # البحث عن درجة الدور الثاني إن وجدت
                second_chance = StudentGrade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject_id,
                    period_id=first_semester.id,  # نستخدم فترة الدور الأول
                    is_second_chance=True
                ).first()

                # تحديد حالة المادة (ناجحة/راسبة) - عكس is_failed
                is_subject_passed = not is_failed

                # تطبيق قواعد نقل الدرجات للعرض
                if is_subject_passed:
                    # المادة ناجحة: عرض الأعمال والامتحان المنقولين
                    transferred_classwork_display = combined_classwork
                    transferred_exam_display = combined_exam
                else:
                    # المادة راسبة: عرض الأعمال المنقولة فقط
                    transferred_classwork_display = combined_classwork
                    transferred_exam_display = 0

                student_data = {
                    'id': student.id,
                    'name': student.name,
                    'national_id': student.national_id,
                    'secret_number': student.secret_number,
                    'seat_number': student.seat_number,
                    'combined_classwork': transferred_classwork_display,  # الأعمال المنقولة
                    'combined_exam': transferred_exam_display,  # الامتحان المنقول
                    'original_combined_exam': combined_exam,  # الامتحان الأصلي للمرجع
                    'is_subject_passed': is_subject_passed,  # حالة المادة (ناجحة/راسبة)
                    'second_chance_exam': second_chance.exam_mark if second_chance else None,
                    'total_mark': second_chance.total_mark if second_chance else None,
                    'is_passed': second_chance.is_passed if second_chance else None
                }

                student_list.append(student_data)

        return jsonify({
            'students': student_list,
            'subject': {
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            },
            'class_name': f"{selected_grade.name} - جميع الفصول"
        })

    # الحصول على قائمة الطلاب المتعثرين (النظام القديم - للتوافق)
    @app.route('/second-chance/students')
    def get_failed_students():
        grade_id = request.args.get('class_id', type=int)  # نبقي اسم المعامل class_id للتوافق مع الواجهة
        subject_id = request.args.get('subject_id', type=int)
        period_id = request.args.get('period_id', type=int)

        if not grade_id or not subject_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الصف والمادة والفترة'}), 400

        # الحصول على المادة
        subject = Subject.query.get_or_404(subject_id)

        # الحصول على جميع الفصول في الصف الدراسي
        all_classes_in_grade = Class.query.filter_by(grade_id=grade_id).all()
        class_ids = [cls.id for cls in all_classes_in_grade]

        # الحصول على الطلاب الراسبين في المادة من جميع فصول الصف الدراسي
        failed_grades = StudentGrade.query.join(Student).filter(
            StudentGrade.subject_id == subject_id,
            StudentGrade.period_id == period_id,
            StudentGrade.is_passed == False,
            StudentGrade.is_second_chance == False,
            Student.class_id.in_(class_ids)
        ).all()

        student_list = []
        for grade in failed_grades:
            student = grade.student

            # البحث عن درجة الدور الثاني إن وجدت
            second_chance = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=True
            ).first()

            student_data = {
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'classwork_mark': grade.classwork_mark,
                'exam_mark': second_chance.exam_mark if second_chance else None,
                'total_mark': second_chance.total_mark if second_chance else None,
                'is_passed': second_chance.is_passed if second_chance else None
            }

            student_list.append(student_data)

        return jsonify({
            'students': student_list,
            'subject': {
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            }
        })

    # حفظ درجات الدور الثاني (النظام الجديد)
    @app.route('/second-chance/save', methods=['POST'])
    def save_second_chance_grades():
        data = request.json
        subject_id = data.get('subject_id')
        grades = data.get('grades', [])

        # التحقق من وجود المادة
        subject = Subject.query.get(subject_id)
        if not subject:
            return jsonify({'success': False, 'message': 'لم يتم العثور على المادة المحددة'})

        # الحصول على فترة الفصل الأول (لحفظ درجة الدور الثاني)
        first_semester = Period.query.filter(
            Period.name == 'الفصل الأول'
        ).first()

        if not first_semester:
            return jsonify({'success': False, 'message': 'لم يتم العثور على فترة الفصل الأول'})

        for grade_data in grades:
            student_id = grade_data.get('student_id')
            exam_mark = grade_data.get('exam_mark')

            # الحصول على درجات الطالب في الفصلين
            first_grade = StudentGrade.query.filter_by(
                student_id=student_id,
                subject_id=subject_id,
                period_id=first_semester.id,
                is_second_chance=False
            ).first()

            second_semester = Period.query.filter(
                Period.name == 'الفصل الثاني'
            ).first()

            second_grade = StudentGrade.query.filter_by(
                student_id=student_id,
                subject_id=subject_id,
                period_id=second_semester.id,
                is_second_chance=False
            ).first() if second_semester else None

            # تطبيق قواعد نقل الدرجات للدور الثاني
            # حساب المجموع المجمع للفصلين لتحديد حالة النجاح/الرسوب
            first_classwork = first_grade.classwork_mark if first_grade and first_grade.classwork_mark is not None else 0
            first_exam = first_grade.exam_mark if first_grade and first_grade.exam_mark is not None else 0
            second_classwork = second_grade.classwork_mark if second_grade and second_grade.classwork_mark is not None else 0
            second_exam = second_grade.exam_mark if second_grade and second_grade.exam_mark is not None else 0

            combined_classwork = first_classwork + second_classwork
            combined_exam = first_exam + second_exam
            total_combined = combined_classwork + combined_exam

            # تحديد حالة النجاح/الرسوب في المادة
            exam_percentage = subject.total_mark * 0.6  # 60% من الدرجة الكلية للمادة
            min_exam_mark = exam_percentage * 0.4  # 40% من درجة الامتحان
            min_total_mark = subject.total_mark * 0.5  # 50% من الدرجة الكلية

            # المادة ناجحة إذا حققت شرط الامتحان وشرط المجموع الكلي
            is_subject_passed = (combined_exam >= min_exam_mark) and (total_combined >= min_total_mark)

            # تطبيق قواعد نقل الدرجات:
            # - المواد الناجحة: نقل الأعمال والامتحان
            # - المواد الراسبة: نقل الأعمال فقط
            if is_subject_passed:
                # المادة ناجحة: نقل الأعمال والامتحان
                transferred_classwork = combined_classwork
                transferred_exam = combined_exam
            else:
                # المادة راسبة: نقل الأعمال فقط
                transferred_classwork = combined_classwork
                transferred_exam = 0  # لا ننقل درجة الامتحان للمواد الراسبة

            # البحث عن درجة الدور الثاني أو إنشاء واحدة جديدة
            second_chance = StudentGrade.query.filter_by(
                student_id=student_id,
                subject_id=subject_id,
                period_id=first_semester.id,
                is_second_chance=True
            ).first()

            if not second_chance:
                second_chance = StudentGrade(
                    student_id=student_id,
                    subject_id=subject_id,
                    period_id=first_semester.id,
                    is_second_chance=True,
                    classwork_mark=transferred_classwork  # الأعمال المنقولة حسب القواعد الجديدة
                )
                # ربط المادة بالدرجة
                second_chance.subject = subject
                db.session.add(second_chance)
            else:
                # تحديث الأعمال المنقولة
                second_chance.classwork_mark = transferred_classwork

            # تحديث درجة الامتحان (تحويل النص إلى رقم)
            try:
                new_exam_mark = float(exam_mark) if exam_mark else None
            except (ValueError, TypeError) as e:
                print(f"خطأ في تحويل درجة الامتحان: {e}")
                print(f"exam_mark: {exam_mark}")
                return jsonify({'success': False, 'message': 'حدث خطأ أثناء تحويل درجة الامتحان. تأكد من إدخال رقم صحيح.'})

            # تطبيق قاعدة درجة الامتحان للدور الثاني:
            # درجة الامتحان = مجموع امتحانات الفصلين + درجة امتحان الدور الثاني
            if new_exam_mark is not None:
                second_chance.exam_mark = transferred_exam + new_exam_mark
            else:
                second_chance.exam_mark = transferred_exam if transferred_exam > 0 else None

            # حساب المجموع الكلي
            if second_chance.classwork_mark is not None and second_chance.exam_mark is not None:
                second_chance.total_mark = second_chance.classwork_mark + second_chance.exam_mark

                # تطبيق نفس معايير النجاح: شرط الامتحان + شرط المجموع الكلي
                exam_percentage = subject.total_mark * 0.6  # 60% من الدرجة الكلية للمادة
                min_exam_mark = exam_percentage * 0.4  # 40% من درجة الامتحان
                min_total_mark = subject.total_mark * 0.5  # 50% من الدرجة الكلية

                second_chance.is_passed = (second_chance.exam_mark >= min_exam_mark) and (second_chance.total_mark >= min_total_mark)
            else:
                second_chance.total_mark = None
                second_chance.is_passed = None

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حفظ درجات الدور الثاني بنجاح'})

    # ===== إدارة الفترات الدراسية =====

    # عرض قائمة الفترات الدراسية
    @app.route('/periods')
    def manage_periods():
        periods = Period.query.all()
        return render_template('periods/manage.html', periods=periods)

    # إضافة فترة دراسية جديدة (تم تعطيل هذه الوظيفة لأن الفترات الدراسية ثابتة)
    @app.route('/periods/add', methods=['POST'])
    def add_period():
        flash('لا يمكن إضافة فترات دراسية جديدة. الفترات الدراسية ثابتة في النظام.', 'error')
        return redirect(url_for('manage_periods'))

    # تحديث العام الدراسي للفترات الدراسية
    @app.route('/periods/update-academic-year', methods=['POST'])
    def update_academic_year():
        academic_year = request.form.get('academic_year')

        # التحقق من البيانات
        if not academic_year:
            flash('يرجى إدخال العام الدراسي', 'error')
            return redirect(url_for('manage_periods'))

        # التحقق من تنسيق العام الدراسي
        if not re.match(r'^\d{4}-\d{4}$', academic_year):
            flash('يرجى إدخال العام الدراسي بالتنسيق الصحيح (مثال: 2024-2025)', 'error')
            return redirect(url_for('manage_periods'))

        # تحديث العام الدراسي لجميع الفترات الدراسية
        periods = Period.query.all()
        for period in periods:
            period.academic_year = academic_year
            # إعادة تعيين حالة الاعتماد
            period.is_approved = False

        db.session.commit()

        flash(f'تم تحديث العام الدراسي إلى {academic_year} بنجاح', 'success')
        return redirect(url_for('manage_periods'))

    # اعتماد فترة دراسية
    @app.route('/periods/approve/<int:id>', methods=['POST'])
    def approve_period(id):
        period = Period.query.get_or_404(id)
        period.is_approved = True
        db.session.commit()

        flash('تم اعتماد الفترة الدراسية بنجاح', 'success')
        return redirect(url_for('manage_periods'))

    # فك اعتماد فترة دراسية
    @app.route('/periods/unapprove/<int:id>', methods=['POST'])
    def unapprove_period(id):
        period = Period.query.get_or_404(id)
        period.is_approved = False
        db.session.commit()

        flash('تم فك اعتماد الفترة الدراسية بنجاح', 'success')
        return redirect(url_for('manage_periods'))

    # ===== التقارير =====

    # صفحة تصدير التقارير الجديدة
    @app.route('/reports/export')
    def reports_export():
        grades = Grade.query.all()
        return render_template('reports/export.html', grades=grades)

    # اختبار Excel بسيط جداً
    @app.route('/reports/simple-excel')
    def simple_excel():
        try:
            from openpyxl import Workbook
            from io import BytesIO

            # إنشاء ملف Excel
            wb = Workbook()
            ws = wb.active

            # بيانات بسيطة
            ws['A1'] = "اختبار Excel"
            ws['A2'] = "يعمل بنجاح!"

            # حفظ في الذاكرة
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # إرجاع الملف
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = 'attachment; filename="test.xlsx"'

            return response

        except Exception as e:
            return f"خطأ: {str(e)}"

    # اختبار أبسط - نص فقط
    @app.route('/reports/test-text')
    def test_text():
        return "هذا اختبار بسيط - إذا ظهر هذا النص فالخادم يعمل"

    # اختبار تحميل ملف نصي
    @app.route('/reports/test-download')
    def test_download():
        try:
            response = make_response("هذا ملف نصي للاختبار")
            response.headers['Content-Type'] = 'text/plain; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename="test.txt"'
            return response
        except Exception as e:
            return f"خطأ: {str(e)}"

    # CSV كبديل لـ Excel
    @app.route('/reports/test-csv')
    def test_csv():
        try:
            import io

            # إنشاء CSV
            output = io.StringIO()
            output.write("ت,اسم الطالب,الفصل,الرياضيات ف1,الرياضيات ف2,المجموع\n")
            output.write("1,أحمد محمد,الفصل الأول,45,48,93\n")
            output.write("2,فاطمة علي,الفصل الأول,42,46,88\n")
            output.write("3,محمد سالم,الفصل الثاني,48,49,97\n")

            # تحويل إلى bytes
            csv_data = output.getvalue().encode('utf-8-sig')  # BOM للعربية

            response = make_response(csv_data)
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = 'attachment; filename="كشف_الدرجات.csv"'

            return response

        except Exception as e:
            return f"خطأ في CSV: {str(e)}"

    # Excel بطريقة مختلفة
    @app.route('/reports/excel-v2')
    def excel_v2():
        try:
            import tempfile
            import os
            from openpyxl import Workbook

            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp:
                wb = Workbook()
                ws = wb.active

                # بيانات بسيطة
                ws['A1'] = "اختبار Excel"
                ws['A2'] = "الطالب الأول"
                ws['A3'] = "الطالب الثاني"

                # حفظ الملف
                wb.save(tmp.name)
                tmp_path = tmp.name

            # قراءة الملف
            with open(tmp_path, 'rb') as f:
                data = f.read()

            # حذف الملف المؤقت
            os.unlink(tmp_path)

            # إرجاع الاستجابة
            response = make_response(data)
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = 'attachment; filename="test_v2.xlsx"'

            return response

        except Exception as e:
            return f"خطأ في Excel V2: {str(e)}"

    # تصدير Excel فوري بدون معاملات
    @app.route('/reports/instant-excel')
    def instant_excel():
        try:
            from openpyxl import Workbook
            from io import BytesIO
            import datetime

            # إنشاء ملف Excel فوري
            wb = Workbook()
            ws = wb.active

            # بيانات فورية
            ws['A1'] = "كشف تجميع الفصلين"
            ws['A2'] = f"التاريخ: {datetime.datetime.now().strftime('%Y/%m/%d')}"

            # رؤوس
            headers = ['ت', 'اسم الطالب', 'الفصل', 'الرياضيات ف1', 'الرياضيات ف2', 'المجموع']
            for col, header in enumerate(headers, 1):
                ws.cell(row=4, column=col, value=header)

            # بيانات
            data = [
                [1, "أحمد محمد", "الفصل الأول", 45, 48, 93],
                [2, "فاطمة علي", "الفصل الأول", 42, 46, 88],
                [3, "محمد سالم", "الفصل الثاني", 48, 49, 97],
            ]

            for row_idx, row_data in enumerate(data, 5):
                for col_idx, value in enumerate(row_data, 1):
                    ws.cell(row=row_idx, column=col_idx, value=value)

            # حفظ
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # إرجاع
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = 'attachment; filename="كشف_فوري.xlsx"'

            return response

        except Exception as e:
            return f"خطأ: {str(e)}"

    # اختبار بسيط لتصدير Excel
    @app.route('/reports/test-excel')
    def test_excel_export():
        try:
            from openpyxl import Workbook
            from io import BytesIO
            import datetime

            # إنشاء ملف Excel بسيط
            wb = Workbook()
            ws = wb.active
            ws.title = "اختبار"

            # إضافة بيانات بسيطة
            ws['A1'] = "اختبار تصدير Excel"
            ws['A2'] = f"التاريخ: {datetime.datetime.now()}"
            ws['A3'] = "هذا اختبار بسيط"

            # إضافة جدول بسيط
            headers = ['ت', 'الاسم', 'الدرجة']
            for col, header in enumerate(headers, 1):
                ws.cell(row=5, column=col, value=header)

            # إضافة بيانات وهمية
            for row in range(6, 11):
                ws.cell(row=row, column=1, value=row-5)
                ws.cell(row=row, column=2, value=f"طالب {row-5}")
                ws.cell(row=row, column=3, value=85 + row)

            # حفظ في الذاكرة
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # إنشاء الاستجابة
            filename = f"اختبار_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            return f"خطأ: {str(e)}"

    # تصدير تقرير تجميع الفصلين - Excel (نسخة بدون قاعدة بيانات)
    @app.route('/reports/export/combine_semesters/excel')
    def export_combine_semesters_excel():
        grade_id = request.args.get('grade_id', type=int)

        if not grade_id:
            return "خطأ: لم يتم تحديد الصف الدراسي"

        try:
            from openpyxl import Workbook
            from io import BytesIO
            import datetime

            # بيانات وهمية للاختبار (بدون قاعدة بيانات)
            grade_name = f"الصف الدراسي {grade_id}"

            # طلاب وهميون
            students_data = [
                {"name": "أحمد محمد علي", "class": "الفصل الأول"},
                {"name": "فاطمة سالم أحمد", "class": "الفصل الأول"},
                {"name": "محمد خالد يوسف", "class": "الفصل الثاني"},
                {"name": "عائشة عبدالله محمد", "class": "الفصل الأول"},
                {"name": "يوسف أحمد سالم", "class": "الفصل الثاني"},
                {"name": "زينب محمد علي", "class": "الفصل الأول"},
                {"name": "عبدالله خالد أحمد", "class": "الفصل الثاني"},
                {"name": "مريم سالم يوسف", "class": "الفصل الأول"},
            ]

            # مواد وهمية
            subjects_data = ["الرياضيات", "العلوم", "اللغة العربية", "التاريخ", "الجغرافيا"]

            # إنشاء ملف Excel بسيط
            wb = Workbook()
            ws = wb.active
            ws.title = f"كشف تجميع الفصلين"

            # إضافة الرأس البسيط
            ws['A1'] = f"كشف تجميع الفصلين - {grade_name}"
            ws['A2'] = f"التاريخ: {datetime.datetime.now().strftime('%Y/%m/%d')}"

            current_row = 4

            # بناء رؤوس الجدول
            headers = ['ت', 'اسم الطالب', 'الفصل']

            # إضافة أعمدة المواد
            for subject in subjects_data:
                headers.extend([
                    f'{subject} ف1',
                    f'{subject} ف2',
                    f'{subject} مجموع'
                ])

            # الأعمدة النهائية
            headers.extend(['المجموع الكلي', 'النسبة %', 'النتيجة'])

            # كتابة الرؤوس
            for col, header in enumerate(headers, 1):
                ws.cell(row=current_row, column=col, value=header)

            current_row += 1

            # إضافة بيانات الطلاب
            for student_index, student in enumerate(students_data, 1):
                col = 1

                # البيانات الأساسية
                ws.cell(row=current_row, column=col, value=student_index)
                col += 1
                ws.cell(row=current_row, column=col, value=student["name"])
                col += 1
                ws.cell(row=current_row, column=col, value=student["class"])
                col += 1

                # درجات المواد (بيانات وهمية للاختبار)
                student_total = 0

                for subject in subjects_data:
                    # درجات وهمية
                    f1_total = 40 + (student_index % 10)  # درجة الفصل الأول
                    f2_total = 45 + (student_index % 15)  # درجة الفصل الثاني
                    subject_total = f1_total + f2_total

                    ws.cell(row=current_row, column=col, value=f1_total)
                    col += 1
                    ws.cell(row=current_row, column=col, value=f2_total)
                    col += 1
                    ws.cell(row=current_row, column=col, value=subject_total)
                    col += 1

                    student_total += subject_total

                # المجموع الكلي
                ws.cell(row=current_row, column=col, value=student_total)
                col += 1

                # النسبة المئوية
                max_total = len(subjects_data) * 100
                percentage = (student_total / max_total * 100) if max_total > 0 else 0
                ws.cell(row=current_row, column=col, value=f"{percentage:.1f}")
                col += 1

                # النتيجة
                result = "ناجح" if percentage >= 50 else "راسب"
                ws.cell(row=current_row, column=col, value=result)

                current_row += 1

            # حفظ الملف في الذاكرة
            output = BytesIO()
            wb.save(output)
            output.seek(0)

            # إنشاء الاستجابة
            filename = f"كشف_تجميع_الفصلين_{grade_name}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            return f"خطأ في تصدير التقرير: {str(e)}"

    # صفحة التقارير
    @app.route('/reports')
    def reports():
        classes = Class.query.all()
        subjects = Subject.query.all()
        periods = Period.query.all()

        return render_template('reports/index.html', classes=classes, subjects=subjects, periods=periods)

    # كشف درجات قابل للطباعة
    @app.route('/reports/grades-sheet')
    def grades_sheet_report():
        class_id = request.args.get('class_id', type=int)
        subject_id = request.args.get('subject_id', type=int)
        period_id = request.args.get('period_id', type=int)

        if not class_id or not subject_id or not period_id:
            flash('يرجى تحديد الفصل والمادة والفترة لعرض الكشف', 'error')
            return redirect(url_for('reports'))

        class_obj = Class.query.get_or_404(class_id)
        subject = Subject.query.get_or_404(subject_id)
        period = Period.query.get_or_404(period_id)
        students = Student.query.filter_by(class_id=class_id).order_by(Student.name).all()

        student_list = []
        for student in students:
            # البحث عن درجة الطالب إن وجدت
            grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=False
            ).first()

            # البحث عن درجة الدور الثاني إن وجدت
            second_chance = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=True
            ).first()

            # استخدام درجة الدور الثاني إذا كانت موجودة وكان الطالب ناجحًا فيها
            final_grade = second_chance if second_chance and second_chance.is_passed else grade

            student_data = {
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'classwork_mark': final_grade.classwork_mark if final_grade else None,
                'exam_mark': final_grade.exam_mark if final_grade else None,
                'total_mark': final_grade.total_mark if final_grade else None,
                'is_passed': final_grade.is_passed if final_grade else None
            }
            student_list.append(student_data)

        return render_template(
            'reports/grades_sheet.html',
            students=student_list,
            subject=subject,
            class_obj=class_obj,
            period=period
        )

    # تقرير درجات الطلاب
    @app.route('/reports/student-grades')
    def student_grades_report():
        classes = Class.query.all()
        subjects = Subject.query.all()
        periods = Period.query.all()

        return render_template('reports/student_grades.html', classes=classes, subjects=subjects, periods=periods)

    # تقرير النتائج النهائية
    @app.route('/reports/final-results')
    def final_results_report():
        classes = Class.query.all()
        subjects = Subject.query.all()
        periods = Period.query.all()

        return render_template('reports/final_results.html', classes=classes, subjects=subjects, periods=periods)

    # صفحة تصدير البيانات
    @app.route('/reports/export-data')
    def export_data():
        classes = Class.query.all()
        periods = Period.query.all()

        return render_template('reports/export_data.html', classes=classes, periods=periods)

    # الحصول على بيانات تقرير درجات الطلاب
    @app.route('/api/report/student-grades')
    def get_student_grades_report():
        class_id = request.args.get('class_id', type=int)
        subject_id = request.args.get('subject_id', type=int)
        period_id = request.args.get('period_id', type=int)

        if not class_id or not subject_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الفصل والمادة والفترة'}), 400

        students = Student.query.filter_by(class_id=class_id).all()
        subject = Subject.query.get_or_404(subject_id)

        student_list = []
        for student in students:
            # البحث عن درجة الطالب إن وجدت
            grade = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=False
            ).first()

            # البحث عن درجة الدور الثاني إن وجدت
            second_chance = StudentGrade.query.filter_by(
                student_id=student.id,
                subject_id=subject_id,
                period_id=period_id,
                is_second_chance=True
            ).first()

            # استخدام درجة الدور الثاني إذا كانت موجودة وكان الطالب ناجحًا فيها
            final_grade = second_chance if second_chance and second_chance.is_passed else grade

            student_data = {
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'classwork_mark': final_grade.classwork_mark if final_grade else None,
                'exam_mark': final_grade.exam_mark if final_grade else None,
                'total_mark': final_grade.total_mark if final_grade else None,
                'is_passed': final_grade.is_passed if final_grade else None
            }

            student_list.append(student_data)

        return jsonify({
            'students': student_list,
            'subject': {
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            }
        })

    # الحصول على بيانات تقرير النتائج النهائية
    @app.route('/api/report/final-results')
    def get_final_results_report():
        class_id = request.args.get('class_id', type=int)
        period_id = request.args.get('period_id', type=int)

        if not class_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الفصل والفترة'}), 400

        students = Student.query.filter_by(class_id=class_id).all()
        subjects = Subject.query.join(Grade).join(Class).filter(Class.id == class_id).all()

        student_list = []
        for student in students:
            subject_grades = []

            for subject in subjects:
                # البحث عن درجة الطالب إن وجدت
                grade = StudentGrade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject.id,
                    period_id=period_id,
                    is_second_chance=False
                ).first()

                # البحث عن درجة الدور الثاني إن وجدت
                second_chance = StudentGrade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject.id,
                    period_id=period_id,
                    is_second_chance=True
                ).first()

                # استخدام درجة الدور الثاني إذا كانت موجودة وكان الطالب ناجحًا فيها
                final_grade = second_chance if second_chance and second_chance.is_passed else grade

                subject_grades.append({
                    'subject_id': subject.id,
                    'subject_name': subject.name,
                    'total_mark': final_grade.total_mark if final_grade else None,
                    'is_passed': final_grade.is_passed if final_grade else None
                })

            from utils.calculations import has_second_chance_exam

            student_data = {
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'subjects': subject_grades,
                'has_second_chance': has_second_chance_exam(student.id)
            }

            student_list.append(student_data)

        return jsonify({
            'students': student_list,
            'subjects': [{'id': subject.id, 'name': subject.name} for subject in subjects]
        })

    # تصدير بيانات الطلاب إلى Excel
    @app.route('/api/export/students')
    def export_students():
        class_id = request.args.get('class_id', type=int)
        return export_students_to_excel(class_id)

    # تصدير بيانات الدرجات إلى Excel
    @app.route('/api/export/grades')
    def export_grades():
        class_id = request.args.get('class_id', type=int)
        period_id = request.args.get('period_id', type=int)
        return export_grades_to_excel(class_id, period_id)

    # تصدير الأرقام السرية وأرقام الجلوس إلى Excel
    @app.route('/api/export/numbers')
    def export_numbers():
        class_id = request.args.get('class_id', type=int)
        return export_numbers_to_excel(class_id)

    # تصدير جميع البيانات إلى Excel
    @app.route('/api/export/all')
    def export_all():
        # تصدير جميع البيانات
        # هنا يمكن تنفيذ كود لتصدير جميع البيانات في ملف واحد
        # ولكن لأغراض هذا المثال، سنقوم بتصدير بيانات الطلاب فقط
        return export_students_to_excel()

    # تصدير تقرير درجات الطلاب إلى Excel
    @app.route('/api/export/student-grades')
    def export_student_grades():
        class_id = request.args.get('class_id', type=int)
        subject_id = request.args.get('subject_id', type=int)
        period_id = request.args.get('period_id', type=int)

        if not class_id or not subject_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الفصل والمادة والفترة'}), 400

        return export_student_grades_report(class_id, subject_id, period_id)

    # تصدير تقرير النتائج النهائية إلى Excel
    @app.route('/api/export/final-results')
    def export_final_results():
        class_id = request.args.get('class_id', type=int)
        period_id = request.args.get('period_id', type=int)

        if not class_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الفصل والفترة'}), 400

        return export_final_results_report(class_id, period_id)

    # ===== التجهيز للامتحانات =====

    # الصفحة الرئيسية للتجهيز للامتحانات
    @app.route('/exam-prep')
    def exam_prep():
        return render_template('exam_prep/index.html')

    # توليد الأرقام السرية
    @app.route('/exam-prep/secret-numbers', methods=['GET', 'POST'])
    def generate_secret_numbers():
        grades = Grade.query.all()

        if request.method == 'POST':
            grade_id = request.form.get('grade_id', type=int)
            pattern = request.form.get('pattern')
            length = request.form.get('length', type=int, default=6)
            prefix = request.form.get('prefix', '')

            if not grade_id or not pattern or not length:
                flash('يرجى ملء جميع الحقول المطلوبة', 'error')
                return redirect(url_for('generate_secret_numbers'))

            # الحصول على طلاب الصف الدراسي
            students = Student.query.join(Class).filter(Class.grade_id == grade_id).all()

            # توليد الأرقام السرية
            if pattern == 'random':
                # أرقام عشوائية
                for student in students:
                    student.secret_number = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

            elif pattern == 'sequential':
                # أرقام متسلسلة
                for i, student in enumerate(students):
                    student.secret_number = str(i + 1).zfill(length)

            elif pattern == 'prefix':
                # بادئة + أرقام متسلسلة
                for i, student in enumerate(students):
                    student.secret_number = prefix + str(i + 1).zfill(length - len(prefix))

            return render_template('exam_prep/secret_numbers.html', students=students, grades=grades)

        return render_template('exam_prep/secret_numbers.html', grades=grades)

    # حفظ الأرقام السرية
    @app.route('/exam-prep/save-secret-numbers', methods=['POST'])
    def save_secret_numbers():
        data = request.json
        secret_numbers = data.get('secret_numbers', [])

        for item in secret_numbers:
            student_id = item.get('student_id')
            secret_number = item.get('secret_number')

            student = Student.query.get(student_id)
            if student:
                student.secret_number = secret_number

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حفظ الأرقام السرية بنجاح'})

    # توليد أرقام الجلوس
    @app.route('/exam-prep/seat-numbers', methods=['GET', 'POST'])
    def generate_seat_numbers():
        grades = Grade.query.all()

        if request.method == 'POST':
            grade_id = request.form.get('grade_id', type=int)
            start_number = request.form.get('start_number', type=int, default=1)

            if not grade_id:
                flash('يرجى اختيار الصف الدراسي', 'error')
                return redirect(url_for('generate_seat_numbers'))

            # الحصول على طلاب الصف الدراسي
            students = Student.query.join(Class).filter(Class.grade_id == grade_id).all()

            # توليد أرقام الجلوس
            for i, student in enumerate(students):
                student.seat_number = start_number + i

            db.session.commit()

            flash('تم توليد أرقام الجلوس بنجاح', 'success')
            return redirect(url_for('generate_seat_numbers'))

        return render_template('exam_prep/seat_numbers.html', grades=grades)

    # توزيع الطلاب على القاعات
    @app.route('/exam-prep/distribute-students')
    def distribute_students():
        grades = Grade.query.all()
        return render_template('exam_prep/distribute.html', grades=grades)

    # طباعة مرايا الجلوس
    @app.route('/exam-prep/print-seat-cards')
    def print_seat_cards():
        grades = Grade.query.all()
        return render_template('exam_prep/print_cards.html', grades=grades)

    # الحصول على قائمة الفصول للصف الدراسي
    @app.route('/api/classes')
    def get_classes():
        grade_id = request.args.get('grade_id', type=int)

        if not grade_id:
            return jsonify({'error': 'يرجى تحديد الصف الدراسي'}), 400

        classes = Class.query.filter_by(grade_id=grade_id).all()

        class_list = []
        for class_obj in classes:
            class_list.append({
                'id': class_obj.id,
                'name': class_obj.name,
                'academic_year': class_obj.academic_year
            })

        return jsonify({'classes': class_list})

    # الحصول على قائمة الطلاب
    @app.route('/api/students')
    def get_students():
        grade_id = request.args.get('grade_id', type=int)
        class_id = request.args.get('class_id', type=int)

        if not grade_id and not class_id:
            return jsonify({'error': 'يرجى تحديد الصف الدراسي أو الفصل'}), 400

        query = Student.query.join(Class)

        if grade_id:
            query = query.filter(Class.grade_id == grade_id)

        if class_id:
            query = query.filter(Student.class_id == class_id)

        students = query.all()

        student_list = []
        for student in students:
            student_list.append({
                'id': student.id,
                'name': student.name,
                'national_id': student.national_id,
                'class_id': student.class_id,
                'class_name': student.class_.name,
                'seat_number': student.seat_number,
                'secret_number': student.secret_number
            })

        return jsonify({'students': student_list})

    # عرض صفحة درجات فصل واحد - جميع المواد
    @app.route('/grades/single-semester')
    def single_semester():
        classes = Class.query.all()
        periods = Period.query.all()
        return render_template('grades/single_semester.html', classes=classes, periods=periods)

    # الحصول على بيانات فصل واحد لجميع الطلاب والمواد
    @app.route('/grades/single-semester/data')
    def get_single_semester_data():
        class_id = request.args.get('class_id', type=int)
        period_id = request.args.get('period_id', type=int)
        
        if not class_id or not period_id:
            return jsonify({'error': 'يرجى تحديد الفصل والفترة الدراسية'}), 400

        # الحصول على الفصل والفترة
        selected_class = Class.query.get_or_404(class_id)
        selected_period = Period.query.get_or_404(period_id)
        
        # الحصول على جميع المواد للفصل
        subjects = Subject.query.filter_by(grade_id=selected_class.grade_id).all()
        
        # الحصول على جميع الطلاب في الفصل
        students = Student.query.filter_by(class_id=class_id).all()
        
        # بناء البيانات
        result_data = {
            'class_name': selected_class.name,
            'period_name': selected_period.name,
            'subjects': [],
            'students': []
        }

        grand_total_possible = 0
        # إضافة معلومات المواد
        for subject in subjects:
            result_data['subjects'].append({
                'id': subject.id,
                'name': subject.name,
                'total_mark': subject.total_mark
            })
            grand_total_possible += subject.total_mark
        
        # إضافة بيانات الطلاب
        for student in students:
            from utils.calculations import has_second_chance_exam

            student_data = {
                'id': student.id,
                'name': student.name,
                'secret_number': student.secret_number,
                'seat_number': student.seat_number,
                'subjects_grades': {},
                'has_second_chance': has_second_chance_exam(student.id)
            }

            student_grand_total = 0
            failed_subjects_indices = []

            # جلب درجات الطالب لكل مادة في الفترة المحددة
            for idx, subject in enumerate(subjects):
                grade = StudentGrade.query.filter_by(
                    student_id=student.id,
                    subject_id=subject.id,
                    period_id=period_id
                ).first()
                
                # حساب البيانات
                classwork_mark = grade.classwork_mark if grade and grade.classwork_mark is not None else None
                exam_mark = grade.exam_mark if grade and grade.exam_mark is not None else None
                
                # حساب المجموع الكلي
                total_mark = None
                is_passed = False

                # حساب درجة الفصل الواحد (50% من الدرجة الكلية للمادة)
                semester_total_mark = subject.total_mark * 0.5

                if classwork_mark is not None and exam_mark is not None:
                    total_mark = classwork_mark + exam_mark
                    is_passed = total_mark >= (semester_total_mark * 0.5)  # 50% من درجة الفصل
                elif classwork_mark is not None:
                    total_mark = classwork_mark
                    is_passed = total_mark >= (semester_total_mark * 0.5)
                elif exam_mark is not None:
                    total_mark = exam_mark
                    is_passed = total_mark >= (semester_total_mark * 0.5)
                
                student_data['subjects_grades'][subject.id] = {
                    'classwork_mark': classwork_mark,
                    'exam_mark': exam_mark,
                    'total_mark': round(total_mark, 1) if total_mark is not None else None,
                    'is_passed': is_passed
                }

                # إضافة إلى المجموع الكلي
                if total_mark is not None:
                    student_grand_total += total_mark

                # إضافة إلى قائمة المواد الراسبة
                if not is_passed:
                    failed_subjects_indices.append(idx + 1)

            # حساب المجموع الكلي والنسبة والنتيجة النهائية للطالب
            student_data['student_grand_total'] = round(student_grand_total, 1)
            student_data['student_percentage'] = round((student_grand_total / grand_total_possible) * 100, 2) if grand_total_possible > 0 else 0

            if not failed_subjects_indices:
                student_data['final_result'] = 'ناجح'
            else:
                failed_subjects_str = ', '.join(map(str, failed_subjects_indices))
                student_data['final_result'] = f'ارجو الاهتمام ({failed_subjects_str})'

            result_data['students'].append(student_data)
        
        return jsonify(result_data)

    return app
