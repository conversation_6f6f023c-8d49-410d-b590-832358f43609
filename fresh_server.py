#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم جديد مع إعادة تحميل قسرية للتعديلات
"""

import sys
import importlib
import os

# إعادة تحميل جميع الوحدات
modules_to_reload = ['app_config', 'app_routes', 'models']
for module_name in modules_to_reload:
    if module_name in sys.modules:
        importlib.reload(sys.modules[module_name])

from flask import Flask
from app_config import configure_app
from app_routes import register_routes
from models import db, init_db, Grade

def create_fresh_app():
    """إنشاء تطبيق جديد مع إعادة تحميل كاملة"""
    app = Flask(__name__)
    
    # تكوين التطبيق
    configure_app(app)
    
    # تسجيل المسارات
    register_routes(app)
    
    # تهيئة قاعدة البيانات
    init_db(app)
    
    # اختبار التعديلات
    with app.app_context():
        grades = Grade.query.all()
        print(f"✅ تم تحميل {len(grades)} صف دراسي:")
        for grade in grades:
            print(f"  - {grade.name}")
    
    return app

if __name__ == '__main__':
    print("🔄 إنشاء خادم جديد مع إعادة تحميل قسرية...")
    print("📝 التعديلات المطلوبة:")
    print("  - تغيير 'اختر الفصل' إلى 'اختر الصف'")
    print("  - عرض الصفوف الدراسية بدلاً من الفصول")
    print("=" * 60)
    
    app = create_fresh_app()
    
    print("🚀 الخادم الجديد يعمل على http://127.0.0.1:5000")
    print("📍 تأكد من إعادة تحميل المتصفح بـ Ctrl+Shift+R")
    print("=" * 60)
    
    try:
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=False,
            threaded=True
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
