#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل خادم Flask للتطبيق
"""

from flask import Flask
from app_config import configure_app
from app_routes import register_routes
from models import db, init_db

def create_app():
    """إنشاء وتكوين تطبيق Flask"""
    app = Flask(__name__)
    
    # تكوين التطبيق
    configure_app(app)
    
    # تسجيل المسارات
    register_routes(app)
    
    # تهيئة قاعدة البيانات
    init_db(app)
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    print("=" * 60)
    print("🚀 تشغيل نظام إدارة درجات الطلبة")
    print("=" * 60)
    print("📍 العنوان: http://127.0.0.1:5000")
    print("🔧 وضع التطوير: مفعل")
    print("📊 التعديلات الجديدة للدور الثاني: مطبقة ✅")
    print("=" * 60)
    print("💡 لإيقاف الخادم: اضغط Ctrl+C")
    print("=" * 60)
    
    try:
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=False  # تعطيل إعادة التحميل التلقائي لتجنب المشاكل
        )
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 تم إيقاف الخادم بنجاح")
        print("=" * 60)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("=" * 60)
