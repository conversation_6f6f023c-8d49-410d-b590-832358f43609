#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعديلات الخاصة بالدور الثاني
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_has_second_chance_exam():
    """اختبار دالة تحديد الطلاب الذين لديهم دور ثان"""
    try:
        from utils.calculations import has_second_chance_exam
        print("✓ تم استيراد دالة has_second_chance_exam بنجاح")
        
        # اختبار مع طالب وهمي
        # في الاختبار الحقيقي، يجب أن يكون هناك طالب في قاعدة البيانات
        print("✓ دالة has_second_chance_exam جاهزة للاستخدام")
        
    except ImportError as e:
        print(f"✗ خطأ في استيراد دالة has_second_chance_exam: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ في اختبار دالة has_second_chance_exam: {e}")
        return False
    
    return True

def test_failed_in_both_semesters():
    """اختبار دالة تحديد الطلاب الذين رسبوا في الفصلين"""
    try:
        from utils.calculations import failed_in_both_semesters
        print("✓ تم استيراد دالة failed_in_both_semesters بنجاح")
        print("✓ دالة failed_in_both_semesters جاهزة للاستخدام")
        
    except ImportError as e:
        print(f"✗ خطأ في استيراد دالة failed_in_both_semesters: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ في اختبار دالة failed_in_both_semesters: {e}")
        return False
    
    return True

def test_calculate_total_grade():
    """اختبار دالة حساب المجموع الكلي مع التعديلات الجديدة"""
    try:
        from utils.calculations import calculate_total_grade
        print("✓ تم استيراد دالة calculate_total_grade بنجاح")
        print("✓ دالة calculate_total_grade تدعم الآن معلومة has_second_chance")
        
    except ImportError as e:
        print(f"✗ خطأ في استيراد دالة calculate_total_grade: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ في اختبار دالة calculate_total_grade: {e}")
        return False
    
    return True

def test_calculate_class_statistics():
    """اختبار دالة حساب إحصائيات الفصل مع التعديلات الجديدة"""
    try:
        from utils.calculations import calculate_class_statistics
        print("✓ تم استيراد دالة calculate_class_statistics بنجاح")
        print("✓ دالة calculate_class_statistics تدعم الآن إحصائيات الطلاب الذين لديهم دور ثان")
        
    except ImportError as e:
        print(f"✗ خطأ في استيراد دالة calculate_class_statistics: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ في اختبار دالة calculate_class_statistics: {e}")
        return False
    
    return True

def test_check_promotion_eligibility():
    """اختبار دالة التحقق من أهلية الترقية مع القواعد الجديدة"""
    try:
        from utils.calculations import check_promotion_eligibility
        print("✓ تم استيراد دالة check_promotion_eligibility بنجاح")
        print("✓ دالة check_promotion_eligibility تدعم الآن الترقية التلقائية للطلاب الراسبين في الفصلين")
        
    except ImportError as e:
        print(f"✗ خطأ في استيراد دالة check_promotion_eligibility: {e}")
        return False
    except Exception as e:
        print(f"✗ خطأ في اختبار دالة check_promotion_eligibility: {e}")
        return False
    
    return True

def test_template_modifications():
    """اختبار وجود ملفات القوالب المعدلة"""
    templates_to_check = [
        'templates/grades/single_semester.html',
        'templates/grades/combine_semesters_all.html',
        'templates/grades/student.html',
        'templates/reports/final_results.html'
    ]
    
    all_exist = True
    for template in templates_to_check:
        if os.path.exists(template):
            print(f"✓ القالب {template} موجود")
        else:
            print(f"✗ القالب {template} غير موجود")
            all_exist = False
    
    return all_exist

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("اختبار التعديلات الخاصة بالدور الثاني")
    print("=" * 60)
    
    tests = [
        ("اختبار دالة has_second_chance_exam", test_has_second_chance_exam),
        ("اختبار دالة failed_in_both_semesters", test_failed_in_both_semesters),
        ("اختبار دالة calculate_total_grade", test_calculate_total_grade),
        ("اختبار دالة calculate_class_statistics", test_calculate_class_statistics),
        ("اختبار دالة check_promotion_eligibility", test_check_promotion_eligibility),
        ("اختبار وجود القوالب المعدلة", test_template_modifications),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        if test_func():
            passed += 1
            print("✓ نجح الاختبار")
        else:
            print("✗ فشل الاختبار")
    
    print("\n" + "=" * 60)
    print(f"النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التعديلات جاهزة للاستخدام.")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
