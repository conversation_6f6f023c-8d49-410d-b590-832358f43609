<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصدير التقارير - نظام إدارة درجات الطلبة</title>
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
        
        body {
            font-family: 'Tajawal', sans-serif;
        }
        
        .rtl {
            direction: rtl;
        }
    </style>
</head>
<body class="bg-gray-100 rtl">
    <!-- Navbar -->
    <nav class="bg-blue-600 text-white shadow-md">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="text-xl font-bold">نظام إدارة درجات الطلبة</div>
                <div class="hidden md:flex space-x-4 space-x-reverse">
                    <a href="/" class="px-3 py-2 rounded hover:bg-blue-700">الرئيسية</a>
                    <a href="/students" class="px-3 py-2 rounded hover:bg-blue-700">الطلاب</a>
                    <a href="/grades" class="px-3 py-2 rounded hover:bg-blue-700">الصفوف الدراسية</a>
                    <a href="/classes" class="px-3 py-2 rounded hover:bg-blue-700">الفصول</a>
                    <a href="/subjects" class="px-3 py-2 rounded hover:bg-blue-700">المواد</a>
                    <a href="/periods" class="px-3 py-2 rounded hover:bg-blue-700">الفترات الدراسية</a>
                    <a href="/grades/record" class="px-3 py-2 rounded hover:bg-blue-700">رصد الدرجات</a>
                    <a href="/grades/combine-semesters-all" class="px-3 py-2 rounded hover:bg-blue-700">تجميع الفصلين - جميع المواد</a>
                    <a href="/grades/single-semester" class="px-3 py-2 rounded hover:bg-blue-700">فصل واحد - جميع المواد</a>
                    <a href="/second-chance" class="px-3 py-2 rounded hover:bg-blue-700">الدور الثاني</a>
                    <a href="/exam-prep" class="px-3 py-2 rounded hover:bg-blue-700">التجهيز للامتحانات</a>
                    <a href="/reports/export" class="px-3 py-2 rounded bg-blue-700">التقارير</a>
                </div>
                <div class="md:hidden">
                    <button id="menu-toggle" class="px-3 py-2 rounded hover:bg-blue-700">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-2">
                <a href="/" class="px-3 py-2 rounded hover:bg-blue-700">الرئيسية</a>
                <a href="/students" class="px-3 py-2 rounded hover:bg-blue-700">الطلاب</a>
                <a href="/grades" class="px-3 py-2 rounded hover:bg-blue-700">الصفوف الدراسية</a>
                <a href="/classes" class="px-3 py-2 rounded hover:bg-blue-700">الفصول</a>
                <a href="/subjects" class="px-3 py-2 rounded hover:bg-blue-700">المواد</a>
                <a href="/periods" class="px-3 py-2 rounded hover:bg-blue-700">الفترات الدراسية</a>
                <a href="/grades/record" class="px-3 py-2 rounded hover:bg-blue-700">رصد الدرجات</a>
                <a href="/grades/combine-semesters-all" class="px-3 py-2 rounded hover:bg-blue-700">تجميع الفصلين - جميع المواد</a>
                <a href="/grades/single-semester" class="px-3 py-2 rounded hover:bg-blue-700">فصل واحد - جميع المواد</a>
                <a href="/second-chance" class="px-3 py-2 rounded hover:bg-blue-700">الدور الثاني</a>
                <a href="/exam-prep" class="px-3 py-2 rounded hover:bg-blue-700">التجهيز للامتحانات</a>
                <a href="/reports/export" class="px-3 py-2 rounded bg-blue-700">التقارير</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
        <!-- العنوان -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">
                <i class="fas fa-file-export text-green-500 mr-3"></i>
                تصدير التقارير
            </h1>
            <p class="text-gray-600">تصدير كشوف الدرجات والتقارير بصيغ مختلفة</p>
        </div>

        <!-- نموذج التصدير -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6">
                <i class="fas fa-cog text-blue-500 mr-2"></i>
                إعدادات التقرير
            </h2>

            <form id="export-form">
                <!-- اختيار نوع التقرير -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-list text-blue-500 mr-1"></i>
                        نوع التقرير
                    </label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="border border-gray-300 rounded-lg p-4 hover:border-blue-500 transition-colors">
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="report_type" value="combine_semesters" class="mr-3" checked>
                                <div>
                                    <div class="font-medium text-gray-800">كشف تجميع الفصلين</div>
                                    <div class="text-sm text-gray-600">درجات الطلاب مجمعة من الفصل الأول والثاني</div>
                                </div>
                            </label>
                        </div>
                        <div class="border border-gray-300 rounded-lg p-4 hover:border-blue-500 transition-colors">
                            <label class="flex items-center cursor-pointer">
                                <input type="radio" name="report_type" value="single_semester" class="mr-3" disabled>
                                <div>
                                    <div class="font-medium text-gray-400">كشف فصل واحد</div>
                                    <div class="text-sm text-gray-400">قريباً...</div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- اختيار الصف -->
                <div class="mb-6">
                    <label for="grade-select" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-users text-blue-500 mr-1"></i>
                        اختر الصف الدراسي
                    </label>
                    <select id="grade-select" name="grade_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        <option value="">-- اختر الصف --</option>
                        {% for grade in grades %}
                        <option value="{{ grade.id }}">{{ grade.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- خيارات التصدير -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-download text-green-500 mr-1"></i>
                        صيغة التصدير
                    </label>
                    <div class="flex space-x-4 space-x-reverse">
                        <label class="flex items-center cursor-pointer">
                            <input type="radio" name="export_format" value="excel" class="mr-2" checked>
                            <i class="fas fa-file-excel text-green-600 mr-1"></i>
                            <span class="text-gray-700">Excel (.xlsx)</span>
                        </label>
                        <label class="flex items-center cursor-pointer opacity-50">
                            <input type="radio" name="export_format" value="pdf" class="mr-2" disabled>
                            <i class="fas fa-file-pdf text-red-600 mr-1"></i>
                            <span class="text-gray-400">PDF (قريباً)</span>
                        </label>
                    </div>
                </div>

                <!-- أزرار العمل -->
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>
                        سيتم تحميل الملف تلقائياً بعد التصدير
                    </div>
                    <div class="flex space-x-3 space-x-reverse">
                        <button type="button" id="preview-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded-md transition duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-eye mr-2"></i>
                            معاينة
                        </button>
                        <button type="submit" id="export-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-6 rounded-md transition duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                            <i class="fas fa-download mr-2"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- مؤشر التحميل -->
        <div id="loading-indicator" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-6 text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
                <p class="text-gray-700">جاري تصدير التقرير...</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-4">
        <div class="container mx-auto px-4 text-center">
            <p>جميع الحقوق محفوظة &copy; 2025 - نظام إدارة درجات الطلبة</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Toggle Mobile Menu
        document.getElementById('menu-toggle').addEventListener('click', function() {
            document.getElementById('mobile-menu').classList.toggle('hidden');
        });

        // العناصر
        const gradeSelect = document.getElementById('grade-select');
        const previewBtn = document.getElementById('preview-btn');
        const exportBtn = document.getElementById('export-btn');
        const exportForm = document.getElementById('export-form');
        const loadingIndicator = document.getElementById('loading-indicator');

        // تفعيل/تعطيل الأزرار
        gradeSelect.addEventListener('change', function() {
            const isSelected = this.value !== '';
            previewBtn.disabled = !isSelected;
            exportBtn.disabled = !isSelected;
        });

        // معالج تصدير التقرير
        exportForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const gradeId = formData.get('grade_id');
            const reportType = formData.get('report_type');
            const exportFormat = formData.get('export_format');
            
            if (!gradeId) {
                alert('يرجى اختيار الصف الدراسي');
                return;
            }
            
            // إظهار مؤشر التحميل
            loadingIndicator.classList.remove('hidden');
            
            // إنشاء رابط التصدير
            const exportUrl = `/reports/export/${reportType}/${exportFormat}?grade_id=${gradeId}`;
            
            // تحميل الملف
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // إخفاء مؤشر التحميل بعد ثانيتين
            setTimeout(() => {
                loadingIndicator.classList.add('hidden');
            }, 2000);
        });

        // معالج المعاينة
        previewBtn.addEventListener('click', function() {
            const gradeId = gradeSelect.value;
            if (gradeId) {
                window.open(`/grades/combine-semesters-all?grade_id=${gradeId}`, '_blank');
            }
        });
    </script>
</body>
</html>
