#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تصدير Excel
"""

import requests
import sys

def test_export():
    try:
        print('🔍 اختبار تصدير Excel...')
        
        # اختبار الرابط مع grade_id=1
        url = 'http://127.0.0.1:5000/reports/export/combine_semesters/excel?grade_id=1'
        print(f'الرابط: {url}')
        
        response = requests.get(url, timeout=30)
        print(f'حالة الاستجابة: {response.status_code}')
        
        if response.status_code == 200:
            print('✅ التصدير نجح!')
            print(f'نوع المحتوى: {response.headers.get("Content-Type", "غير محدد")}')
            print(f'حجم الملف: {len(response.content)} بايت')
            
            # حفظ الملف للاختبار
            with open('test_export.xlsx', 'wb') as f:
                f.write(response.content)
            print('✅ تم حفظ الملف: test_export.xlsx')
            
        elif response.status_code == 500:
            print('❌ خطأ في الخادم (500)')
            print('محتوى الخطأ:')
            print(response.text[:1000])
            
        elif response.status_code == 404:
            print('❌ الصفحة غير موجودة (404)')
            
        else:
            print(f'❌ خطأ غير متوقع: {response.status_code}')
            print('محتوى الاستجابة:')
            print(response.text[:500])
            
    except requests.exceptions.Timeout:
        print('❌ انتهت مهلة الاتصال')
        
    except requests.exceptions.ConnectionError:
        print('❌ خطأ في الاتصال - تأكد من تشغيل الخادم')
        
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == '__main__':
    test_export()
