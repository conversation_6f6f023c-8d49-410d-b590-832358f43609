{% extends "base.html" %}

{% block title %}تجميع درجات الفصلين - جميع المواد{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- العنوان -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">
            <i class="fas fa-calculator text-blue-500 mr-2"></i>
            تجميع درجات الفصلين - جميع المواد
        </h1>
        <p class="text-gray-600">عرض جميع الطلاب ودرجاتهم المجمعة لجميع المواد</p>
    </div>

    <!-- اختيار الفصل -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="class-select" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-users text-blue-500 mr-1"></i>
                    اختر الفصل
                </label>
                <select id="class-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">-- اختر الفصل --</option>
                    {% for class in classes %}
                    <option value="{{ class.id }}">{{ class.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="flex items-end">
                <button id="load-data-btn" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>
                    <i class="fas fa-download mr-2"></i>
                    تحميل البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- مؤشر التحميل -->
    <div id="loading-indicator" class="hidden text-center py-10">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">جاري تحميل البيانات...</p>
    </div>

    <!-- رسالة عدم وجود بيانات -->
    <div id="no-data-message" class="hidden text-center py-10 text-gray-500">
        <i class="fas fa-exclamation-circle text-5xl mb-4 text-gray-300"></i>
        <p>لا توجد بيانات متاحة</p>
    </div>

    <!-- عرض البيانات -->
    <div id="data-container" class="hidden">
        <!-- معلومات الفصل -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-2">
                <i class="fas fa-info-circle text-green-500 mr-2"></i>
                معلومات الفصل: <span id="class-name" class="text-blue-600"></span>
            </h2>
            <p class="text-sm text-gray-500 mt-2">
                <i class="fas fa-clock mr-1"></i>
                آخر تحديث: <span id="last-update" class="font-medium">-</span>
            </p>
        </div>

        <!-- جدول البيانات -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead class="bg-gray-50">
                        <tr id="main-header">
                            <th class="py-3 px-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r">م</th>
                            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r">اسم الطالب</th>
                            <th class="py-3 px-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r">الرقم السري</th>
                            <th class="py-3 px-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r">رقم الجلوس</th>
                            <!-- سيتم إضافة رؤوس المواد هنا -->
                        </tr>
                        <tr id="sub-header" class="bg-gray-100">
                            <th class="py-2 px-2 border-b border-r"></th>
                            <th class="py-2 px-4 border-b border-r"></th>
                            <th class="py-2 px-2 border-b border-r"></th>
                            <th class="py-2 px-2 border-b border-r"></th>
                            <!-- سيتم إضافة رؤوس فرعية للمواد هنا -->
                        </tr>
                    </thead>
                    <tbody id="data-table-body" class="bg-white">
                        <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
    /* تحسين عرض الجدول */
    .summary-columns {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-left: 3px solid #3b82f6;
    }

    .total-column {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border-left: 2px solid #f59e0b;
    }

    .percentage-column {
        background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        border-left: 2px solid #10b981;
    }

    .result-column {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        border-left: 2px solid #3b82f6;
    }

    .passed-result {
        background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%) !important;
        color: #065f46 !important;
        font-weight: bold;
        border: 1px solid #10b981;
    }

    .failed-result {
        background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%) !important;
        color: #991b1b !important;
        font-weight: bold;
        border: 1px solid #ef4444;
    }

    /* تحسين عرض الجدول على الشاشات الصغيرة */
    @media (max-width: 768px) {
        .summary-columns {
            min-width: 120px;
        }
    }
</style>
<script>
    // العناصر
    const classSelect = document.getElementById('class-select');
    const loadDataBtn = document.getElementById('load-data-btn');
    const loadingIndicator = document.getElementById('loading-indicator');
    const noDataMessage = document.getElementById('no-data-message');
    const dataContainer = document.getElementById('data-container');
    const classNameSpan = document.getElementById('class-name');
    const subjectsHeader = document.getElementById('subjects-header');
    const dataTableBody = document.getElementById('data-table-body');

    // تفعيل/تعطيل زر التحميل
    classSelect.addEventListener('change', function() {
        loadDataBtn.disabled = !this.value;
    });

    // متغير لحفظ الفصل المحدد حالياً
    let currentClassId = null;
    
    // دالة تحميل البيانات
    function loadData(classId) {
        if (!classId) {
            alert('يرجى اختيار الفصل');
            return;
        }

        // إخفاء المحتوى وإظهار مؤشر التحميل
        dataContainer.classList.add('hidden');
        noDataMessage.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');

        // طلب البيانات من الخادم
        fetch(`/grades/combine-semesters/all-data?class_id=${classId}`)
            .then(response => {
                loadingIndicator.classList.add('hidden');
                
                if (!response.ok) {
                    throw new Error('حدث خطأ أثناء تحميل البيانات');
                }
                
                return response.json();
            })
            .then(data => {
                if (data.students && data.students.length > 0 && data.subjects && data.subjects.length > 0) {
                    displayData(data);
                    
                    // إظهار إشعار التحديث الناجح
                    showUpdateNotification('تم تحديث البيانات بنجاح', 'success');
                } else {
                    noDataMessage.classList.remove('hidden');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.classList.add('hidden');
                alert(error.message || 'حدث خطأ أثناء تحميل البيانات');
            });
    }
    
    // تحميل البيانات عند الضغط على الزر
    loadDataBtn.addEventListener('click', function() {
        const classId = classSelect.value;
        currentClassId = classId;
        loadData(classId);
    });
    
    // تحديث البيانات تلقائياً كل 30 ثانية إذا كان هناك فصل محدد
    setInterval(function() {
        if (currentClassId && !dataContainer.classList.contains('hidden')) {
            loadData(currentClassId);
        }
    }, 30000); // 30 ثانية
    
    // إضافة زر تحديث يدوي
    function addRefreshButton() {
        if (document.getElementById('refresh-btn')) return; // تجنب إضافة الزر مرتين
        
        const refreshBtn = document.createElement('button');
        refreshBtn.id = 'refresh-btn';
        refreshBtn.className = 'ml-2 bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition duration-200';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>تحديث';
        refreshBtn.onclick = function() {
            if (currentClassId) {
                loadData(currentClassId);
            }
        };
        
        loadDataBtn.parentNode.appendChild(refreshBtn);
     }
     
     // دالة إظهار الإشعارات
     function showUpdateNotification(message, type = 'info') {
         // إزالة الإشعارات السابقة
         const existingNotification = document.getElementById('update-notification');
         if (existingNotification) {
             existingNotification.remove();
         }
         
         // إنشاء الإشعار الجديد
         const notification = document.createElement('div');
         notification.id = 'update-notification';
         notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;
         
         if (type === 'success') {
             notification.className += ' bg-green-500 text-white';
             notification.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
         } else if (type === 'error') {
             notification.className += ' bg-red-500 text-white';
             notification.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
         } else {
             notification.className += ' bg-blue-500 text-white';
             notification.innerHTML = `<i class="fas fa-info-circle mr-2"></i>${message}`;
         }
         
         document.body.appendChild(notification);
         
         // إظهار الإشعار
         setTimeout(() => {
             notification.classList.remove('translate-x-full');
         }, 100);
         
         // إخفاء الإشعار بعد 3 ثوان
         setTimeout(() => {
             notification.classList.add('translate-x-full');
             setTimeout(() => {
                 if (notification.parentNode) {
                     notification.remove();
                 }
             }, 300);
         }, 3000);
     }

     // عرض البيانات
    function displayData(data) {
        // عرض اسم الفصل
        classNameSpan.textContent = data.class_name;
        
        // تحديث وقت آخر تحديث
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('last-update').textContent = timeString;

        // بناء رؤوس المواد
        const mainHeader = document.getElementById('main-header');
        const subHeader = document.getElementById('sub-header');
        
        // إزالة الرؤوس القديمة إذا وجدت (نحتفظ بـ 4 أعمدة أساسية فقط)
        while (mainHeader.children.length > 4) {
            mainHeader.removeChild(mainHeader.lastChild);
        }
        while (subHeader.children.length > 4) {
            subHeader.removeChild(subHeader.lastChild);
        }

        // إضافة رؤوس المواد الرئيسية
        data.subjects.forEach(subject => {
            const th = document.createElement('th');
            th.className = 'py-3 px-1 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-r bg-blue-50';
            th.setAttribute('colspan', '5');
            th.innerHTML = `<div class="font-semibold text-blue-800">${subject.name}</div><div class="text-xs text-gray-600">(${subject.total_mark} درجة)</div>`;
            mainHeader.appendChild(th);
        });

        // إضافة الأعمدة الثلاثة الجديدة في النهاية
        const totalHeader = document.createElement('th');
        totalHeader.className = 'py-3 px-3 text-center text-xs font-medium text-gray-700 uppercase tracking-wider border-b border-r total-column';
        totalHeader.textContent = 'المجموع الكلي';
        mainHeader.appendChild(totalHeader);

        const percentageHeader = document.createElement('th');
        percentageHeader.className = 'py-3 px-3 text-center text-xs font-medium text-gray-700 uppercase tracking-wider border-b border-r percentage-column';
        percentageHeader.textContent = 'النسبة المئوية';
        mainHeader.appendChild(percentageHeader);

        const resultHeader = document.createElement('th');
        resultHeader.className = 'py-3 px-4 text-center text-xs font-medium text-gray-700 uppercase tracking-wider border-b border-r result-column';
        resultHeader.textContent = 'النتيجة النهائية';
        mainHeader.appendChild(resultHeader);

        // إضافة الرؤوس الفرعية للمواد
        data.subjects.forEach(subject => {
            ['أعمال ف1', 'امتحان ف1', 'أعمال ف2', 'امتحان ف2', 'المجموع'].forEach(label => {
                const th = document.createElement('th');
                th.className = 'py-2 px-1 text-center text-xs text-gray-600 border-b border-r';
                th.textContent = label;
                subHeader.appendChild(th);
            });
        });

        // إضافة الرؤوس الفرعية للأعمدة الثلاثة الجديدة
        const totalSubHeader = document.createElement('th');
        totalSubHeader.className = 'py-2 px-3 text-center text-xs text-gray-700 border-b border-r total-column';
        totalSubHeader.textContent = 'الدرجات';
        subHeader.appendChild(totalSubHeader);

        const percentageSubHeader = document.createElement('th');
        percentageSubHeader.className = 'py-2 px-3 text-center text-xs text-gray-700 border-b border-r percentage-column';
        percentageSubHeader.textContent = '%';
        subHeader.appendChild(percentageSubHeader);

        const resultSubHeader = document.createElement('th');
        resultSubHeader.className = 'py-2 px-4 text-center text-xs text-gray-700 border-b border-r result-column';
        resultSubHeader.textContent = 'الحالة';
        subHeader.appendChild(resultSubHeader);

        // ملء بيانات الطلاب
        dataTableBody.innerHTML = '';
        
        data.students.forEach((student, studentIndex) => {
            const row = document.createElement('tr');
            row.className = studentIndex % 2 === 0 ? 'bg-white hover:bg-blue-50' : 'bg-gray-50 hover:bg-blue-100';
            
            let rowHTML = `
                <td class="py-3 px-2 text-center text-sm border-r font-medium">${studentIndex + 1}</td>
                <td class="py-3 px-4 text-right text-sm border-r">
                    <div class="font-semibold text-gray-900">${student.name}</div>
                </td>
                <td class="py-3 px-2 text-center text-sm border-r">${student.secret_number || '-'}</td>
                <td class="py-3 px-2 text-center text-sm border-r">${student.seat_number || '-'}</td>
            `;
            
            // إضافة درجات كل مادة
            data.subjects.forEach(subject => {
                const grades = student.subjects_grades[subject.id];

                if (grades) {
                    // تحديد لون خلية المجموع بناءً على النجاح/الرسوب
                    let totalClass = '';
                    if (grades.total_mark !== null) {
                        totalClass = grades.is_passed ? 'text-green-700 font-bold bg-green-100' : 'text-red-700 font-bold bg-red-100';
                    }

                    rowHTML += `
                        <td class="py-3 px-1 text-center text-sm border-r">${grades.first_semester.classwork_mark !== null ? grades.first_semester.classwork_mark : '-'}</td>
                        <td class="py-3 px-1 text-center text-sm border-r">${grades.first_semester.exam_mark !== null ? grades.first_semester.exam_mark : '-'}</td>
                        <td class="py-3 px-1 text-center text-sm border-r">${grades.second_semester.classwork_mark !== null ? grades.second_semester.classwork_mark : '-'}</td>
                        <td class="py-3 px-1 text-center text-sm border-r">${grades.second_semester.exam_mark !== null ? grades.second_semester.exam_mark : '-'}</td>
                        <td class="py-3 px-1 text-center text-sm border-r ${totalClass} rounded">${grades.total_mark !== null ? grades.total_mark : '-'}</td>
                    `;
                } else {
                    rowHTML += `
                        <td class="py-3 px-1 text-center text-sm border-r text-gray-400">-</td>
                        <td class="py-3 px-1 text-center text-sm border-r text-gray-400">-</td>
                        <td class="py-3 px-1 text-center text-sm border-r text-gray-400">-</td>
                        <td class="py-3 px-1 text-center text-sm border-r text-gray-400">-</td>
                        <td class="py-3 px-1 text-center text-sm border-r text-gray-400">-</td>
                    `;
                }
            });

            // إضافة الأعمدة الثلاثة الجديدة
            // تحديد ألوان النتيجة النهائية
            let resultClass = 'result-column';
            let resultText = student.final_result || '-';
            let totalDisplay = '-';
            let percentageDisplay = '-';

            if (resultText.includes('ناجح')) {
                resultClass += ' passed-result';
                // إظهار المجموع والنسبة فقط للطلاب الناجحين وليس لديهم دور ثان
                if (student.has_second_chance) {
                    // الطلاب الذين لديهم دور ثان: لا نعرض المجموع والنسبة
                    totalDisplay = '-';
                    percentageDisplay = '-';
                } else {
                    totalDisplay = student.student_grand_total || '-';
                    percentageDisplay = student.student_percentage ? student.student_percentage + '%' : '-';
                }
            } else if (resultText.includes('دور ثان')) {
                resultClass += ' failed-result';
                // عدم إظهار المجموع والنسبة للطلاب الذين لديهم دور ثان
                totalDisplay = '-';
                percentageDisplay = '-';
            }

            rowHTML += `
                <td class="py-3 px-3 text-center text-sm border-r font-bold total-column">${totalDisplay}</td>
                <td class="py-3 px-3 text-center text-sm border-r font-bold percentage-column">${percentageDisplay}</td>
                <td class="py-3 px-4 text-center text-sm border-r ${resultClass} rounded">${resultText}</td>
            `;
            
            row.innerHTML = rowHTML;
            dataTableBody.appendChild(row);
        });

        // إظهار الجدول
        dataContainer.classList.remove('hidden');
        
        // إضافة زر التحديث إذا لم يكن موجوداً
        addRefreshButton();
    }
</script>
{% endblock %}