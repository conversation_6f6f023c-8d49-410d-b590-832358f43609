#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف اختبار الخادم
"""

from flask import Flask
from app_config import configure_app
from app_routes import register_routes
from models import db, init_db

def create_app():
    """إنشاء وتكوين تطبيق Flask"""
    app = Flask(__name__)
    
    # تكوين التطبيق
    configure_app(app)
    
    # تسجيل المسارات
    register_routes(app)
    
    # تهيئة قاعدة البيانات
    init_db(app)
    
    return app

if __name__ == '__main__':
    app = create_app()
    
    print("🚀 تشغيل خادم الاختبار")
    print("📍 العنوان: http://127.0.0.1:5000")
    
    try:
        app.run(
            debug=True,
            host='127.0.0.1',
            port=5000,
            use_reloader=False
        )
    except Exception as e:
        print(f"❌ خطأ: {e}")
