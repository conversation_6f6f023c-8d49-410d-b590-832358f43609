# دليل استخدام تعديلات الدور الثاني

## نظرة عامة

تم تطبيق تعديلات شاملة على نظام رصد الدرجات لتحسين التعامل مع الطلاب الذين لديهم دور ثان. هذه التعديلات تطبق القواعد الجديدة للدور الثاني وتحسن من دقة النظام.

## القواعد الجديدة

### 1. عرض المجموع والنسبة المئوية
- **للطلاب العاديين**: يتم عرض المجموع الكلي والنسبة المئوية كالمعتاد
- **للطلاب الذين لديهم دور ثان**: لا يتم عرض المجموع الكلي والنسبة المئوية
- **السبب**: الطلاب الذين لديهم دور ثان لم يكملوا جميع امتحاناتهم بعد

### 2. نقل الدرجات للدور الثاني

#### المواد الناجحة:
- ✅ **الأعمال**: تنقل من الفصلين (مجموع الفصل الأول + الفصل الثاني)
- ✅ **الامتحان**: ينقل من الفصلين (مجموع امتحان الفصل الأول + الفصل الثاني)

#### المواد الراسبة:
- ✅ **الأعمال**: تنقل من الفصلين (مجموع الفصل الأول + الفصل الثاني)
- ❌ **الامتحان**: لا ينقل (يبدأ من الصفر في الدور الثاني)

### 3. حساب درجة الامتحان في الدور الثاني
```
درجة الامتحان النهائية = الدرجة المنقولة + درجة امتحان الدور الثاني
```

### 4. معايير النجاح
- **شرط الامتحان**: 40% من درجة الامتحان الكلية
- **شرط المجموع**: 50% من الدرجة الكلية للمادة
- **النجاح**: تحقيق كلا الشرطين معاً

### 5. الترقية التلقائية
- الطلاب الذين رسبوا في **جميع المواد** في **الفصلين** يتم ترقيتهم تلقائياً

## كيفية استخدام النظام

### 1. رصد درجات الدور الثاني
1. انتقل إلى صفحة "الدور الثاني"
2. اختر الفصل والمادة
3. ستظهر قائمة بالطلاب المتعثرين مع:
   - الأعمال المنقولة
   - الامتحان المنقول (للمواد الناجحة فقط)
   - خانة لإدخال درجة امتحان الدور الثاني

### 2. عرض درجات الطلاب
- **الطلاب العاديين**: يظهر المجموع والنسبة كالمعتاد
- **الطلاب الذين لديهم دور ثان**: يظهر "-" بدلاً من المجموع والنسبة
- **رسالة توضيحية**: تظهر للطلاب الذين لديهم دور ثان

### 3. التقارير والكشوف
- جميع التقارير تطبق القواعد الجديدة تلقائياً
- الطلاب الذين لديهم دور ثان يظهرون بدون مجموع أو نسبة
- الإحصائيات تستبعد الطلاب الذين لديهم دور ثان من المتوسطات

## الملفات المعدلة

### ملفات الحسابات:
- `utils/calculations.py`
- `student_management_system/utils/calculations.py`

### ملفات الواجهات:
- `templates/grades/single_semester.html`
- `templates/grades/combine_semesters_all.html`
- `templates/grades/student.html`
- `templates/reports/final_results.html`

### ملفات الخادم:
- `app_routes.py`

## الدوال الجديدة

### `has_second_chance_exam(student_id)`
```python
# تحديد ما إذا كان الطالب له دور ثان
result = has_second_chance_exam(123)
# True إذا كان له دور ثان، False إذا لم يكن له دور ثان
```

### `failed_in_both_semesters(student_id, class_id)`
```python
# تحديد ما إذا كان الطالب رسب في جميع المواد في الفصلين
result = failed_in_both_semesters(123, 456)
# True إذا رسب في جميع المواد، False إذا نجح في مادة واحدة على الأقل
```

## اختبار النظام

لاختبار التعديلات، شغل الأمر التالي:
```bash
python test_second_chance_modifications.py
```

يجب أن تحصل على النتيجة:
```
🎉 جميع الاختبارات نجحت! التعديلات جاهزة للاستخدام.
```

## استكشاف الأخطاء

### المشكلة: لا يظهر المجموع للطلاب العاديين
**الحل**: تأكد من أن الطالب ليس له درجة دور ثاني في قاعدة البيانات

### المشكلة: الأعمال لا تنقل بشكل صحيح
**الحل**: تأكد من وجود درجات للطالب في الفصلين

### المشكلة: معايير النجاح لا تطبق بشكل صحيح
**الحل**: تأكد من أن درجات المادة محددة بشكل صحيح في قاعدة البيانات

## الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من ملف `SECOND_CHANCE_MODIFICATIONS.md` للتفاصيل التقنية
2. شغل ملف الاختبار للتأكد من سلامة النظام
3. راجع الأخطاء في سجلات النظام

## ملاحظات مهمة

- ⚠️ **لا تعدل** الدوال الجديدة بدون فهم كامل للنظام
- ✅ **النسخ الاحتياطية** متوفرة في تاريخ Git
- 🔄 **التحديثات** قابلة للتراجع عنها إذا لزم الأمر
- 📊 **الإحصائيات** تحسب تلقائياً مع القواعد الجديدة

---
**آخر تحديث**: 2025-06-26  
**الإصدار**: 1.0  
**الحالة**: مكتمل ومختبر ✅
