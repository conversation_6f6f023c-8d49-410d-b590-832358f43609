{% extends 'base.html' %}

{% block title %}درجات الطالب - نظام إدارة درجات الطلبة{% endblock %}

{% block page_title %}درجات الطالب: {{ student.full_name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-graduate me-2"></i>
                    بيانات الطالب
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <tr>
                        <th style="width: 30%">الاسم الكامل</th>
                        <td>{{ student.full_name }}</td>
                    </tr>
                    <tr>
                        <th>الرقم الوطني</th>
                        <td>{{ student.national_id }}</td>
                    </tr>
                    <tr>
                        <th>الرقم السري</th>
                        <td>{{ student.secret_number }}</td>
                    </tr>
                    <tr>
                        <th>رقم الجلوس</th>
                        <td>{{ student.seat_number or 'غير محدد' }}</td>
                    </tr>
                    <tr>
                        <th>الصف</th>
                        <td>{{ student.class_obj.name }} ({{ student.class_obj.stage.name }})</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    ملخص الدرجات
                </h5>
            </div>
            <div class="card-body">
                {% if total_info and not total_info.get('has_second_chance', False) %}
                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h3 class="text-primary mb-0">{{ "%.1f"|format(total_info.total_marks) }}</h3>
                                <p class="text-muted">المجموع الكلي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h3 class="text-primary mb-0">{{ "%.1f"|format(total_info.percentage) }}%</h3>
                                <p class="text-muted">النسبة المئوية</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% elif total_info and total_info.get('has_second_chance', False) %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الطالب له دور ثان. لا يتم عرض المجموع الكلي والنسبة المئوية للطلاب الذين لديهم دور ثان.
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لم يتم رصد أي درجات لهذا الطالب بعد.
                </div>
                {% endif %}

                <div class="mt-3">
                    <a href="{{ url_for('reports.student_report', student_id=student.id) }}" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-1"></i>
                        تحميل تقرير الدرجات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            جدول الدرجات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th rowspan="2" class="align-middle">المادة</th>
                        {% for period in periods %}
                        <th colspan="3" class="text-center">{{ period.name }}</th>
                        {% endfor %}
                        <th rowspan="2" class="align-middle text-center">المجموع</th>
                        <th rowspan="2" class="align-middle text-center">النتيجة</th>
                    </tr>
                    <tr>
                        {% for period in periods %}
                        <th class="text-center">أعمال</th>
                        <th class="text-center">امتحان</th>
                        <th class="text-center">المجموع</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for class_subject in class_subjects %}
                    <tr>
                        <td>{{ class_subject.stage_subject.subject.name }}</td>

                        {% set subject_total = 0 %}
                        {% set period_count = 0 %}
                        {% set all_passed = true %}

                        {% for period in periods %}
                            {% set grade_info = grades[class_subject.id][period.id] %}

                            <td class="text-center">
                                {% if grade_info.classwork is not none %}
                                    {{ "%.1f"|format(grade_info.classwork) }}
                                    {% set subject_total = subject_total + grade_info.total %}
                                    {% set period_count = period_count + 1 %}
                                    {% if not grade_info.is_passed %}
                                        {% set all_passed = false %}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                                <a href="{{ url_for('grades.add_student_grade', student_id=student.id, class_subject_id=class_subject.id, period_id=period.id) }}" class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                            <td class="text-center">
                                {% if grade_info.exam is not none %}
                                    {% if grade_info.second_chance_exam_mark is not none %}
                                        <span class="text-danger">{{ "%.1f"|format(grade_info.exam) }}</span>
                                        <span class="badge bg-warning">د2: {{ "%.1f"|format(grade_info.second_chance_exam_mark) }}</span>
                                    {% else %}
                                        {{ "%.1f"|format(grade_info.exam) }}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if grade_info.total is not none %}
                                    {{ "%.1f"|format(grade_info.total) }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        {% endfor %}

                        <td class="text-center fw-bold">
                            {% if period_count > 0 %}
                                {% set subject_average = subject_total / period_count %}
                                {{ "%.1f"|format(subject_average) }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if period_count > 0 %}
                                {% if all_passed %}
                                    <span class="badge bg-success">ناجح</span>
                                {% else %}
                                    <span class="badge bg-danger">راسب</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">غير مقيم</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                {% if total_info %}
                <tfoot class="table-light">
                    <tr>
                        <th>المجموع الكلي</th>
                        {% for period in periods %}
                        <th colspan="3" class="text-center">-</th>
                        {% endfor %}
                        <th class="text-center">{{ "%.1f"|format(total_info.total_marks) }}</th>
                        <th class="text-center">{{ "%.1f"|format(total_info.percentage) }}%</th>
                    </tr>
                </tfoot>
                {% endif %}
            </table>
        </div>
    </div>
</div>

<div class="mt-4">
    <a href="{{ url_for('grades.search') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-1"></i>
        رجوع للبحث
    </a>
</div>
{% endblock %}
