#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار صفحة تجميع الفصلين
"""

import requests
from bs4 import BeautifulSoup

def test_page():
    try:
        print('🔍 اختبار الصفحة...')
        response = requests.get('http://127.0.0.1:5000/grades/combine-semesters-all', timeout=10)
        print(f'حالة الاستجابة: {response.status_code}')
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # البحث عن النص 'اختر الصف'
            label = soup.find('label', {'for': 'class-select'})
            if label and 'اختر الصف' in label.text:
                print('✅ تم العثور على النص: اختر الصف')
            else:
                print('❌ لم يتم العثور على النص: اختر الصف')
                if label:
                    print(f'النص الموجود: {label.text.strip()}')
            
            # البحث عن خيارات القائمة
            select = soup.find('select', {'id': 'class-select'})
            if select:
                options = select.find_all('option')
                print(f'عدد الخيارات: {len(options)}')
                for i, option in enumerate(options[:5]):  # أول 5 خيارات
                    value = option.get('value', '')
                    text = option.text.strip()
                    print(f'  {i}: {text} (value={value})')
            else:
                print('❌ لم يتم العثور على القائمة المنسدلة')
        else:
            print(f'❌ خطأ في الاستجابة: {response.status_code}')
            
    except Exception as e:
        print(f'❌ خطأ: {e}')

if __name__ == '__main__':
    test_page()
