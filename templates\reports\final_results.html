{% extends 'base.html' %}

{% block content %}
<div class="bg-white rounded-lg shadow-md p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-blue-600">تقرير النتائج النهائية</h1>
        <a href="{{ url_for('reports') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition-colors">
            <i class="fas fa-arrow-right ml-1"></i> العودة
        </a>
    </div>
    
    <!-- نموذج اختيار الفصل والفترة -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 class="text-lg font-semibold mb-4">اختر الفصل والفترة</h2>
        
        <form id="report-form">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="class-select" class="block text-gray-700 font-medium mb-2">الفصل</label>
                    <select id="class-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="" disabled selected>اختر الفصل</option>
                        {% for class in classes %}
                        <option value="{{ class.id }}">{{ class.name }} - {{ class.grade.name }} ({{ class.grade.track }})</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="period-select" class="block text-gray-700 font-medium mb-2">الفترة</label>
                    <select id="period-select" class="w-full border border-gray-300 rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" disabled>
                        <option value="" disabled selected>اختر الفترة</option>
                        {% for period in periods %}
                        <option value="{{ period.id }}">{{ period.name }} - {{ period.academic_year }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <button type="button" id="generate-report-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-6 rounded transition-colors" disabled>
                    <i class="fas fa-chart-bar ml-1"></i> عرض التقرير
                </button>
            </div>
        </form>
    </div>
    
    <!-- نتائج التقرير -->
    <div id="report-container" class="hidden">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">النتائج النهائية</h2>
            <div class="flex space-x-2 space-x-reverse">
                <button id="print-btn" class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-print ml-1"></i> طباعة
                </button>
                <button id="export-excel-btn" class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-2 px-4 rounded transition-colors">
                    <i class="fas fa-file-excel ml-1"></i> تصدير Excel
                </button>
            </div>
        </div>
        
        <div id="report-header" class="text-center mb-4 print:block hidden">
            <h1 class="text-xl font-bold">تقرير النتائج النهائية</h1>
            <div class="flex justify-center space-x-4 space-x-reverse mt-2">
                <p>الفصل: <span id="print-class"></span></p>
                <p>الفترة: <span id="print-period"></span></p>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="py-3 px-4 border-b text-right">#</th>
                        <th class="py-3 px-4 border-b text-right">الاسم</th>
                        <th class="py-3 px-4 border-b text-right">الرقم الوطني</th>
                        <th id="subjects-header" colspan="1" class="py-3 px-4 border-b text-center">المواد</th>
                        <th class="py-3 px-4 border-b text-center">المجموع</th>
                        <th class="py-3 px-4 border-b text-center">النسبة المئوية</th>
                        <th class="py-3 px-4 border-b text-center">النتيجة</th>
                    </tr>
                </thead>
                <tbody id="report-table-body">
                    <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
        
        <div class="mt-4">
            <div class="flex justify-between">
                <div>
                    <p>عدد الطلاب: <span id="total-students" class="font-semibold"></span></p>
                </div>
                <div>
                    <p>عدد الناجحين: <span id="passed-students" class="font-semibold text-green-600"></span></p>
                    <p>عدد الراسبين: <span id="failed-students" class="font-semibold text-red-600"></span></p>
                    <p>نسبة النجاح: <span id="pass-rate" class="font-semibold"></span></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- مؤشر التحميل -->
    <div id="loading-indicator" class="hidden text-center py-10">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">جاري تحميل البيانات...</p>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        
        #report-container, #report-container * {
            visibility: visible;
        }
        
        #report-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 20px;
        }
        
        #print-btn, #export-excel-btn {
            display: none;
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
    // العناصر
    const classSelect = document.getElementById('class-select');
    const periodSelect = document.getElementById('period-select');
    const generateReportBtn = document.getElementById('generate-report-btn');
    const reportContainer = document.getElementById('report-container');
    const loadingIndicator = document.getElementById('loading-indicator');
    const reportTableBody = document.getElementById('report-table-body');
    const subjectsHeader = document.getElementById('subjects-header');
    const totalStudentsSpan = document.getElementById('total-students');
    const passedStudentsSpan = document.getElementById('passed-students');
    const failedStudentsSpan = document.getElementById('failed-students');
    const passRateSpan = document.getElementById('pass-rate');
    const printBtn = document.getElementById('print-btn');
    const exportExcelBtn = document.getElementById('export-excel-btn');
    const printClassSpan = document.getElementById('print-class');
    const printPeriodSpan = document.getElementById('print-period');
    
    // تفعيل/تعطيل الحقول
    classSelect.addEventListener('change', function() {
        if (this.value) {
            periodSelect.disabled = false;
        } else {
            periodSelect.disabled = true;
            generateReportBtn.disabled = true;
        }
        
        // إعادة تعيين الاختيارات
        periodSelect.selectedIndex = 0;
        generateReportBtn.disabled = true;
    });
    
    periodSelect.addEventListener('change', function() {
        generateReportBtn.disabled = !this.value;
    });
    
    // عرض التقرير
    generateReportBtn.addEventListener('click', function() {
        const classId = classSelect.value;
        const periodId = periodSelect.value;
        
        if (!classId || !periodId) {
            alert('يرجى اختيار الفصل والفترة');
            return;
        }
        
        // إظهار مؤشر التحميل
        reportContainer.classList.add('hidden');
        loadingIndicator.classList.remove('hidden');
        
        // طلب البيانات من الخادم
        fetch(`/api/report/final-results?class_id=${classId}&period_id=${periodId}`)
            .then(response => response.json())
            .then(data => {
                // إخفاء مؤشر التحميل
                loadingIndicator.classList.add('hidden');
                
                if (data.students && data.students.length > 0 && data.subjects && data.subjects.length > 0) {
                    // تحديث معلومات الطباعة
                    printClassSpan.textContent = classSelect.options[classSelect.selectedIndex].text;
                    printPeriodSpan.textContent = periodSelect.options[periodSelect.selectedIndex].text;
                    
                    // تحديث عدد المواد في الجدول
                    subjectsHeader.colSpan = data.subjects.length;
                    
                    // ملء جدول التقرير
                    reportTableBody.innerHTML = '';
                    
                    let passedCount = 0;
                    
                    data.students.forEach((student, index) => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';
                        
                        // حساب عدد المواد التي نجح فيها الطالب
                        const passedSubjects = student.subjects.filter(subject => subject.is_passed).length;
                        
                        // تحديد ما إذا كان الطالب ناجحًا في جميع المواد
                        const isAllPassed = passedSubjects === student.subjects.length;
                        
                        if (isAllPassed) {
                            passedCount++;
                        }
                        
                        // بناء خلايا المواد
                        let subjectCells = '';
                        student.subjects.forEach(subject => {
                            subjectCells += `<td class="py-3 px-4 border-b text-center ${subject.is_passed ? 'text-green-600' : 'text-red-600'}">${subject.total_mark ? (subject.total_mark * 100).toFixed(2) : '-'}</td>`;
                        });
                        
                        // حساب المجموع والنسبة المئوية (فقط للطلاب الذين ليس لديهم دور ثان)
                        const totalMarks = student.subjects.reduce((sum, subject) => sum + (subject.total_mark || 0), 0);
                        const percentage = (totalMarks / student.subjects.length * 100).toFixed(2);

                        // التحقق من وجود دور ثان للطالب
                        const hasSecondChance = student.has_second_chance || false;
                        const totalDisplay = hasSecondChance ? '-' : totalMarks.toFixed(2);
                        const percentageDisplay = hasSecondChance ? '-' : percentage + '%';

                        row.innerHTML = `
                            <td class="py-3 px-4 border-b text-center">${index + 1}</td>
                            <td class="py-3 px-4 border-b text-right">${student.name}</td>
                            <td class="py-3 px-4 border-b text-center">${student.national_id}</td>
                            ${subjectCells}
                            <td class="py-3 px-4 border-b text-center font-semibold">${totalDisplay}</td>
                            <td class="py-3 px-4 border-b text-center font-semibold">${percentageDisplay}</td>
                            <td class="py-3 px-4 border-b text-center font-semibold ${isAllPassed ? 'text-green-600' : 'text-red-600'}">${isAllPassed ? 'ناجح' : 'راسب'}</td>
                        `;
                        
                        reportTableBody.appendChild(row);
                    });
                    
                    // تحديث الإحصائيات
                    const totalStudents = data.students.length;
                    const failedCount = totalStudents - passedCount;
                    const passRate = (passedCount / totalStudents * 100).toFixed(2);
                    
                    totalStudentsSpan.textContent = totalStudents;
                    passedStudentsSpan.textContent = passedCount;
                    failedStudentsSpan.textContent = failedCount;
                    passRateSpan.textContent = passRate + '%';
                    
                    // إظهار التقرير
                    reportContainer.classList.remove('hidden');
                } else {
                    alert('لا توجد بيانات للعرض');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                loadingIndicator.classList.add('hidden');
                alert('حدث خطأ أثناء تحميل البيانات');
            });
    });
    
    // طباعة التقرير
    printBtn.addEventListener('click', function() {
        window.print();
    });
    
    // تصدير التقرير إلى Excel
    exportExcelBtn.addEventListener('click', function() {
        const classId = classSelect.value;
        const periodId = periodSelect.value;
        
        window.location.href = `/api/export/final-results?class_id=${classId}&period_id=${periodId}`;
    });
</script>
{% endblock %}
